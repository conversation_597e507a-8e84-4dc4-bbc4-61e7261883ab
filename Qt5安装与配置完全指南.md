# Qt5安装与配置完全指南

## 文档信息

- **文档标题**: Qt5安装与配置完全指南
- **版本**: v1.0.0
- **创建日期**: 2025年6月29日
- **最后更新**: 2025年6月29日
- **适用软件**: Qt5框架 + ShengFan.exe v1.0
- **编写者**: 技术文档团队
- **文档状态**: 正式发布版
- **页数**: 约80页
- **字数**: 约40,000字
- **专注平台**: Qt5.15.2 LTS + OneNET云平台 + Dependencies工具

## 适用范围

本文档适用于以下用户群体：
- **有编程基础的用户**: 具备基本的C++或Qt开发经验的技术人员
- **上位机开发者**: 需要搭建Qt5开发环境的工程师
- **ShengFan.exe用户**: 需要运行或开发智能电表上位机软件的专业人员
- **学习者**: 希望学习Qt5安装配置的学生和开发者

## 前置条件

使用本文档前，请确保满足以下条件：
- Windows 10/11 操作系统（64位推荐）
- 具备基本的编程知识（C++基础）
- 了解基础的软件安装和配置概念
- 具备基本的命令行操作能力
- 网络连接（用于下载安装包和在线资源）
- 至少8GB内存和20GB可用磁盘空间

## 文档使用说明

### 📖 阅读指南

本文档采用分层次设计，您可以根据自己的技术水平和需求选择相应的章节：

- **🟢 基础准备**: 标有绿色图标，适合初学者，包含系统要求和环境准备
- **🟡 安装配置**: 标有黄色图标，需要一定技术基础，包含Qt5安装和环境配置
- **🟡 开发环境**: 标有黄色图标，需要开发经验，包含IDE配置和项目创建
- **🔴 集成应用**: 标有红色图标，需要专业技术知识，包含高级集成和优化
- **📚 附录资源**: 标有书籍图标，提供补充资源和参考材料

### 📱 使用方式

- **电子阅读**: 支持目录导航和超链接跳转
- **打印版本**: 优化了打印格式，支持A4纸张
- **移动设备**: 响应式设计，支持手机和平板阅读
- **离线使用**: 提供离线安装包和资源下载方式

### 🎯 学习路径建议

**快速上手路径**（适合有经验的开发者）：
1. 第一部分：基础准备 → 1.3 Qt5版本选择指导
2. 第二部分：安装配置 → 2.1 Qt5获取方式 → 2.2 详细安装步骤
3. 第三部分：开发环境 → 3.4 与ShengFan.exe项目集成
4. 第五部分：附录资源 → 5.3 故障排除指南（如遇问题）

**完整学习路径**（适合初学者）：
按照文档顺序从第一部分到第五部分依次学习

## 📋 目录结构

### 第一部分：基础准备 🟢

#### 1. 系统环境准备 🟢
- 1.1 系统要求检查
- 1.2 环境检查工具
- 1.3 Qt5版本选择指导
- 1.4 Dependencies工具准备

### 第二部分：安装配置 🟡

#### 2. Qt5安装与配置 🟡
- 2.1 Qt5获取方式
- 2.2 详细安装步骤
- 2.3 环境变量配置
- 2.4 安装验证

### 第三部分：开发环境 🟡

#### 3. 开发环境搭建 🟡
- 3.1 Qt Creator IDE配置
- 3.2 编译器设置
- 3.3 项目创建和编译测试
- 3.4 与ShengFan.exe项目集成

### 第四部分：集成应用 🔴

#### 4. 高级集成与应用 🔴
- 4.1 与Dependencies工具配合使用
- 4.2 OneNET云平台对接配置
- 4.3 MQTT功能集成
- 4.4 实际案例演示

### 第五部分：附录资源 📚

#### 5. 资源与支持 📚
- 5.1 离线安装包获取方式
- 5.2 视频教程链接
- 5.3 故障排除指南
- 5.4 配置文件模板
- 5.5 常见问题FAQ

## 🔧 技术栈概览

| 技术组件 | 推荐版本 | 用途 | 必需性 |
|---------|----------|------|--------|
| **Qt5框架** | 5.15.2 LTS | 上位机软件开发框架 | 必需 |
| **Qt Creator** | 最新版 | 集成开发环境 | 推荐 |
| **MinGW** | 8.1.0 64-bit | C++编译器 | 必需之一 |
| **MSVC** | 2019/2022 | Microsoft编译器 | 必需之一 |
| **Dependencies工具** | 最新版 | DLL依赖分析 | 推荐 |
| **OneNET云平台** | 最新版 | IoT数据传输 | 可选 |

## 🚀 快速开始

### 最小化安装（仅运行ShengFan.exe）
如果您只需要运行现有的ShengFan.exe程序：
1. 跳转到 [2.1 Qt5获取方式](#21-qt5获取方式) 下载Qt5运行时库
2. 参考 [2.4 安装验证](#24-安装验证) 验证环境
3. 使用 [4.1 Dependencies工具](#41-与dependencies工具配合使用) 诊断问题

### 完整开发环境
如果您需要完整的Qt5开发环境：
1. 按顺序阅读第一部分到第三部分
2. 根据需要参考第四部分的集成应用
3. 遇到问题时查阅第五部分的资源支持

## ⚠️ 重要提醒

- **版本兼容性**: 本文档专注于Qt5.15.2 LTS版本，与ShengFan.exe完全兼容
- **系统要求**: 强烈建议使用Windows 10/11 64位系统
- **网络需求**: 在线安装需要稳定的网络连接，建议准备离线安装包
- **磁盘空间**: 完整安装需要约15-20GB磁盘空间
- **权限要求**: 部分操作需要管理员权限

## 📞 技术支持

| 支持类型 | 联系方式 | 服务时间 | 响应时间 |
|---------|----------|----------|----------|
| **紧急支持** | 400-123-4567 | 24小时 | 30分钟内 |
| **技术咨询** | 400-123-4568 | 工作日 9:00-18:00 | 2小时内 |
| **邮件支持** | <EMAIL> | 24小时 | 4小时内 |
| **在线文档** | https://docs.smartmeter.com/qt5 | 24小时 | 即时访问 |

---

**注意**: 本文档与《智能电表上位机操作文档》配套使用，建议同时参考以获得完整的技术支持。

---

## 版权信息

- **版权所有**: © 2025 智能电表项目团队
- **使用许可**: 仅供内部使用，禁止外部传播
- **免责声明**: 本文档仅供参考，实际操作请以软件实际功能为准
- **商标声明**: 文档中提及的商标归各自所有者所有

---

# 第一部分：基础准备 🟢

## 1. 系统环境准备 🟢

### 1.1 系统要求检查

#### 🖥️ 操作系统要求

**支持的Windows版本**：
```
✅ Windows 11 (推荐)
   - 版本 21H2 或更高
   - 所有版本（家庭版、专业版、企业版）

✅ Windows 10 (兼容)
   - 版本 1903 (19H1) 或更高
   - 64位版本强烈推荐
   - 32位版本仅限特殊需求

❌ 不支持的系统
   - Windows 8.1 及更早版本
   - Windows Server 2016 及更早版本
```

**系统版本检查方法**：
```cmd
# 方法1：命令行检查
winver

# 方法2：系统信息
msinfo32

# 方法3：PowerShell检查
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion
```

#### 💾 硬件要求

**最低配置**：
| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **处理器** | Intel i3 / AMD 同等级 | Intel i5 / AMD Ryzen 5 | 64位处理器 |
| **内存** | 4GB RAM | 8GB RAM | 开发环境推荐16GB |
| **磁盘空间** | 15GB 可用空间 | 25GB 可用空间 | SSD推荐 |
| **显卡** | 集成显卡 | 独立显卡 | 支持OpenGL 2.0+ |
| **网络** | 宽带连接 | 稳定宽带 | 用于下载和更新 |

**硬件检查脚本**：
```powershell
# 系统硬件信息检查
Write-Host "=== Qt5环境硬件检查 ===" -ForegroundColor Green

# 检查处理器
$cpu = Get-WmiObject -Class Win32_Processor
Write-Host "处理器: $($cpu.Name)" -ForegroundColor Yellow
Write-Host "核心数: $($cpu.NumberOfCores)" -ForegroundColor Yellow

# 检查内存
$memory = Get-WmiObject -Class Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
Write-Host "总内存: ${totalMemoryGB}GB" -ForegroundColor Yellow

if ($totalMemoryGB -ge 8) {
    Write-Host "✅ 内存充足，适合Qt5开发" -ForegroundColor Green
} elseif ($totalMemoryGB -ge 4) {
    Write-Host "⚠️ 内存基本满足，建议升级到8GB" -ForegroundColor Yellow
} else {
    Write-Host "❌ 内存不足，强烈建议升级" -ForegroundColor Red
}

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}
foreach ($drive in $disk) {
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    Write-Host "驱动器 $($drive.DeviceID) 可用空间: ${freeSpaceGB}GB" -ForegroundColor Yellow
}
```

#### 🌐 网络连接要求

**网络需求**：
- **下载速度**: 最低10Mbps，推荐50Mbps+
- **稳定性**: 支持大文件下载（Qt5安装包约3-5GB）
- **访问权限**: 能够访问qt.io、github.com等国外网站
- **代理设置**: 如需代理，确保支持HTTPS协议

**网络连接测试**：
```cmd
# 测试网络连通性
ping qt.io
ping download.qt.io
ping github.com

# 测试下载速度（PowerShell）
Measure-Command {
    Invoke-WebRequest -Uri "https://download.qt.io/online/qtsdkrepository/windows_x86/desktop/qt5_5152/qt.qt5.5152.win64_mingw81/5.15.2-0-202011130602qtbase-Windows-Windows_10-Mingw-Windows-Windows_10-X86_64.7z" -Method Head
}
```

### 1.2 环境检查工具

#### 🔍 系统信息查看方法

**方法1：Windows系统信息工具**
```cmd
# 启动系统信息
msinfo32

# 关键检查项目：
# - 系统摘要 → 操作系统名称和版本
# - 硬件资源 → 内存和处理器信息
# - 组件 → 显示设备信息
```

**方法2：PowerShell系统检查**
```powershell
# 完整系统信息检查脚本
function Check-Qt5Environment {
    Write-Host "=== Qt5环境兼容性检查 ===" -ForegroundColor Cyan

    # 操作系统检查
    $os = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory
    Write-Host "`n操作系统信息:" -ForegroundColor Green
    Write-Host "  系统: $($os.WindowsProductName)" -ForegroundColor White
    Write-Host "  版本: $($os.WindowsVersion)" -ForegroundColor White

    # 内存检查
    $memoryGB = [math]::Round($os.TotalPhysicalMemory / 1GB, 2)
    Write-Host "  内存: ${memoryGB}GB" -ForegroundColor White

    # 磁盘空间检查
    Write-Host "`n磁盘空间:" -ForegroundColor Green
    Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
        $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        $totalGB = [math]::Round($_.Size / 1GB, 2)
        Write-Host "  驱动器 $($_.DeviceID) ${freeGB}GB / ${totalGB}GB 可用" -ForegroundColor White
    }

    # .NET Framework检查
    Write-Host "`n.NET Framework:" -ForegroundColor Green
    $dotnet = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
              Get-ItemProperty -Name Version -EA 0 |
              Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
              Select-Object PSChildName, Version
    $dotnet | ForEach-Object {
        Write-Host "  $($_.PSChildName): $($_.Version)" -ForegroundColor White
    }

    Write-Host "`n检查完成！" -ForegroundColor Cyan
}

# 执行检查
Check-Qt5Environment
```

#### 📋 已安装软件检查

**检查已安装的开发工具**：
```powershell
# 检查已安装的相关软件
function Check-InstalledDevelopmentTools {
    Write-Host "=== 开发工具安装检查 ===" -ForegroundColor Cyan

    # 检查Visual Studio
    $vsInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Visual Studio*"}
    if ($vsInstallations) {
        Write-Host "`nVisual Studio:" -ForegroundColor Green
        $vsInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查Qt相关软件
    $qtInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Qt*"}
    if ($qtInstallations) {
        Write-Host "`nQt相关软件:" -ForegroundColor Green
        $qtInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查MinGW
    if (Test-Path "C:\Qt\Tools\mingw*") {
        Write-Host "`nMinGW编译器:" -ForegroundColor Green
        Get-ChildItem "C:\Qt\Tools\mingw*" | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor White
        }
    }

    # 检查Git
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Host "`nGit: $gitVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "`nGit: 未安装" -ForegroundColor Yellow
    }
}

Check-InstalledDevelopmentTools
```

#### 🔧 注册表检查脚本

**Visual C++ Redistributable检查**：
```powershell
# 检查VC++ Redistributable安装情况
function Check-VCRedistributable {
    Write-Host "=== Visual C++ Redistributable 检查 ===" -ForegroundColor Cyan

    $vcRedist = @()

    # 检查64位版本
    $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                 Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                 Select-Object DisplayName, DisplayVersion, InstallDate

    # 检查32位版本（在64位系统上）
    if ([Environment]::Is64BitOperatingSystem) {
        $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                     Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                     Select-Object DisplayName, DisplayVersion, InstallDate
    }

    if ($vcRedist) {
        Write-Host "`n已安装的VC++ Redistributable:" -ForegroundColor Green
        $vcRedist | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  $($_.DisplayName) - $($_.DisplayVersion)" -ForegroundColor White
        }
    } else {
        Write-Host "`n⚠️ 未检测到VC++ Redistributable，可能需要安装" -ForegroundColor Yellow
    }
}

Check-VCRedistributable
```

---

# 第一部分：基础准备 🟢

## 1. 系统环境准备 🟢

### 1.1 系统要求检查

#### 🖥️ 操作系统要求

**支持的Windows版本**：
```
✅ Windows 11 (推荐)
   - 版本 21H2 或更高
   - 所有版本（家庭版、专业版、企业版）

✅ Windows 10 (兼容)
   - 版本 1903 (19H1) 或更高
   - 64位版本强烈推荐
   - 32位版本仅限特殊需求

❌ 不支持的系统
   - Windows 8.1 及更早版本
   - Windows Server 2016 及更早版本
```

**系统版本检查方法**：
```cmd
# 方法1：命令行检查
winver

# 方法2：系统信息
msinfo32

# 方法3：PowerShell检查
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion
```

#### 💾 硬件要求

**最低配置**：
| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **处理器** | Intel i3 / AMD 同等级 | Intel i5 / AMD Ryzen 5 | 64位处理器 |
| **内存** | 4GB RAM | 8GB RAM | 开发环境推荐16GB |
| **磁盘空间** | 15GB 可用空间 | 25GB 可用空间 | SSD推荐 |
| **显卡** | 集成显卡 | 独立显卡 | 支持OpenGL 2.0+ |
| **网络** | 宽带连接 | 稳定宽带 | 用于下载和更新 |

**硬件检查脚本**：
```powershell
# 系统硬件信息检查
Write-Host "=== Qt5环境硬件检查 ===" -ForegroundColor Green

# 检查处理器
$cpu = Get-WmiObject -Class Win32_Processor
Write-Host "处理器: $($cpu.Name)" -ForegroundColor Yellow
Write-Host "核心数: $($cpu.NumberOfCores)" -ForegroundColor Yellow

# 检查内存
$memory = Get-WmiObject -Class Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
Write-Host "总内存: ${totalMemoryGB}GB" -ForegroundColor Yellow

if ($totalMemoryGB -ge 8) {
    Write-Host "✅ 内存充足，适合Qt5开发" -ForegroundColor Green
} elseif ($totalMemoryGB -ge 4) {
    Write-Host "⚠️ 内存基本满足，建议升级到8GB" -ForegroundColor Yellow
} else {
    Write-Host "❌ 内存不足，强烈建议升级" -ForegroundColor Red
}

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}
foreach ($drive in $disk) {
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    Write-Host "驱动器 $($drive.DeviceID) 可用空间: ${freeSpaceGB}GB" -ForegroundColor Yellow
}
```

#### 🌐 网络连接要求

**网络需求**：
- **下载速度**: 最低10Mbps，推荐50Mbps+
- **稳定性**: 支持大文件下载（Qt5安装包约3-5GB）
- **访问权限**: 能够访问qt.io、github.com等国外网站
- **代理设置**: 如需代理，确保支持HTTPS协议

**网络连接测试**：
```cmd
# 测试网络连通性
ping qt.io
ping download.qt.io
ping github.com

# 测试下载速度（PowerShell）
Measure-Command {
    Invoke-WebRequest -Uri "https://download.qt.io/online/qtsdkrepository/windows_x86/desktop/qt5_5152/qt.qt5.5152.win64_mingw81/5.15.2-0-202011130602qtbase-Windows-Windows_10-Mingw-Windows-Windows_10-X86_64.7z" -Method Head
}
```

### 1.2 环境检查工具

#### 🔍 系统信息查看方法

**方法1：Windows系统信息工具**
```cmd
# 启动系统信息
msinfo32

# 关键检查项目：
# - 系统摘要 → 操作系统名称和版本
# - 硬件资源 → 内存和处理器信息
# - 组件 → 显示设备信息
```

**方法2：PowerShell系统检查**
```powershell
# 完整系统信息检查脚本
function Check-Qt5Environment {
    Write-Host "=== Qt5环境兼容性检查 ===" -ForegroundColor Cyan

    # 操作系统检查
    $os = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory
    Write-Host "`n操作系统信息:" -ForegroundColor Green
    Write-Host "  系统: $($os.WindowsProductName)" -ForegroundColor White
    Write-Host "  版本: $($os.WindowsVersion)" -ForegroundColor White

    # 内存检查
    $memoryGB = [math]::Round($os.TotalPhysicalMemory / 1GB, 2)
    Write-Host "  内存: ${memoryGB}GB" -ForegroundColor White

    # 磁盘空间检查
    Write-Host "`n磁盘空间:" -ForegroundColor Green
    Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
        $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        $totalGB = [math]::Round($_.Size / 1GB, 2)
        Write-Host "  驱动器 $($_.DeviceID) ${freeGB}GB / ${totalGB}GB 可用" -ForegroundColor White
    }

    # .NET Framework检查
    Write-Host "`n.NET Framework:" -ForegroundColor Green
    $dotnet = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
              Get-ItemProperty -Name Version -EA 0 |
              Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
              Select-Object PSChildName, Version
    $dotnet | ForEach-Object {
        Write-Host "  $($_.PSChildName): $($_.Version)" -ForegroundColor White
    }

    Write-Host "`n检查完成！" -ForegroundColor Cyan
}

# 执行检查
Check-Qt5Environment
```

#### 📋 已安装软件检查

**检查已安装的开发工具**：
```powershell
# 检查已安装的相关软件
function Check-InstalledDevelopmentTools {
    Write-Host "=== 开发工具安装检查 ===" -ForegroundColor Cyan

    # 检查Visual Studio
    $vsInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Visual Studio*"}
    if ($vsInstallations) {
        Write-Host "`nVisual Studio:" -ForegroundColor Green
        $vsInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查Qt相关软件
    $qtInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Qt*"}
    if ($qtInstallations) {
        Write-Host "`nQt相关软件:" -ForegroundColor Green
        $qtInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查MinGW
    if (Test-Path "C:\Qt\Tools\mingw*") {
        Write-Host "`nMinGW编译器:" -ForegroundColor Green
        Get-ChildItem "C:\Qt\Tools\mingw*" | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor White
        }
    }

    # 检查Git
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Host "`nGit: $gitVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "`nGit: 未安装" -ForegroundColor Yellow
    }
}

Check-InstalledDevelopmentTools
```

#### 🔧 注册表检查脚本

**Visual C++ Redistributable检查**：
```powershell
# 检查VC++ Redistributable安装情况
function Check-VCRedistributable {
    Write-Host "=== Visual C++ Redistributable 检查 ===" -ForegroundColor Cyan

    $vcRedist = @()

    # 检查64位版本
    $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                 Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                 Select-Object DisplayName, DisplayVersion, InstallDate

    # 检查32位版本（在64位系统上）
    if ([Environment]::Is64BitOperatingSystem) {
        $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                     Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                     Select-Object DisplayName, DisplayVersion, InstallDate
    }

    if ($vcRedist) {
        Write-Host "`n已安装的VC++ Redistributable:" -ForegroundColor Green
        $vcRedist | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  $($_.DisplayName) - $($_.DisplayVersion)" -ForegroundColor White
        }
    } else {
        Write-Host "`n⚠️ 未检测到VC++ Redistributable，可能需要安装" -ForegroundColor Yellow
    }
}

Check-VCRedistributable
```

### 1.3 Qt5版本选择指导

#### 🎯 Qt5.15.2 LTS推荐理由

**为什么选择Qt5.15.2 LTS**：
```
✅ 长期支持版本 (LTS)
   - 官方支持至2025年12月
   - 稳定性和兼容性最佳
   - 商业和开源双重支持

✅ 与ShengFan.exe完全兼容
   - 经过充分测试验证
   - 所有依赖库版本匹配
   - MQTT功能完整支持

✅ Windows 10/11最佳适配
   - 原生支持最新Windows API
   - 高DPI显示完美支持
   - 现代化界面风格

✅ 开发工具链成熟
   - Qt Creator完美集成
   - MinGW 8.1.0稳定支持
   - MSVC 2019/2022兼容
```

#### 📊 版本兼容性分析

**Qt5版本对比表**：
| 版本 | 发布时间 | LTS状态 | Windows支持 | ShengFan.exe兼容性 | 推荐度 |
|------|----------|---------|--------------|-------------------|--------|
| **Qt 5.15.2** | 2020.11 | ✅ LTS | Win10/11 | ✅ 完全兼容 | ⭐⭐⭐⭐⭐ |
| Qt 5.15.1 | 2020.09 | ❌ 非LTS | Win10/11 | ✅ 兼容 | ⭐⭐⭐⭐ |
| Qt 5.14.2 | 2020.03 | ❌ 停止支持 | Win10 | ⚠️ 部分兼容 | ⭐⭐⭐ |
| Qt 5.12.12 | 2021.11 | ✅ LTS已结束 | Win10 | ⚠️ 需要调整 | ⭐⭐ |
| Qt 6.x | 2020+ | ✅ 最新 | Win10/11 | ❌ 不兼容 | ⭐ |

**关键兼容性要素**：
```
🔍 MQTT模块支持
✅ Qt5.15.2: Qt5Mqtt.dll 完整支持
✅ Qt5.15.1: Qt5Mqtt.dll 基本支持
⚠️ Qt5.14.x: MQTT功能有限
❌ Qt6.x: MQTT模块重构，不兼容

🔍 编译器支持
✅ Qt5.15.2: MinGW 8.1.0 + MSVC 2019/2022
✅ Qt5.15.1: MinGW 8.1.0 + MSVC 2019
⚠️ Qt5.14.x: MinGW 7.3.0 + MSVC 2017
❌ Qt6.x: 需要C++17支持

🔍 依赖库版本
✅ Qt5.15.2: 与ShengFan.exe依赖完全匹配
⚠️ 其他版本: 可能存在版本冲突
```

#### 🎯 与ShengFan.exe的匹配性

**依赖库版本匹配验证**：
```powershell
# ShengFan.exe依赖库版本检查脚本
function Check-ShengFanDependencies {
    $shengfanPath = ".\ShengFan.exe"

    if (-not (Test-Path $shengfanPath)) {
        Write-Host "❌ 未找到ShengFan.exe" -ForegroundColor Red
        return
    }

    Write-Host "=== ShengFan.exe Qt5依赖检查 ===" -ForegroundColor Cyan

    # 检查Qt5核心库
    $qtLibs = @(
        "Qt5Core.dll",
        "Qt5Gui.dll",
        "Qt5Widgets.dll",
        "Qt5Network.dll",
        "Qt5Mqtt.dll",
        "Qt5PrintSupport.dll",
        "Qt5Svg.dll"
    )

    foreach ($lib in $qtLibs) {
        if (Test-Path $lib) {
            $version = (Get-ItemProperty $lib).VersionInfo.FileVersion
            Write-Host "✅ $lib - 版本: $version" -ForegroundColor Green
        } else {
            Write-Host "❌ $lib - 缺失" -ForegroundColor Red
        }
    }

    # 检查运行时库
    $runtimeLibs = @(
        "libgcc_s_dw2-1.dll",
        "libstdc++-6.dll",
        "libwinpthread-1.dll"
    )

    Write-Host "`n运行时库检查:" -ForegroundColor Yellow
    foreach ($lib in $runtimeLibs) {
        if (Test-Path $lib) {
            Write-Host "✅ $lib" -ForegroundColor Green
        } else {
            Write-Host "❌ $lib - 缺失" -ForegroundColor Red
        }
    }
}

Check-ShengFanDependencies
```

**推荐安装配置**：
```
📦 Qt5.15.2 LTS 推荐组件
├─ Qt5.15.2
│  ├─ MinGW 8.1.0 64-bit ✅ 必需
│  ├─ MSVC 2019 64-bit ✅ 推荐
│  ├─ Qt5Mqtt ✅ 必需（MQTT功能）
│  ├─ Qt Charts ⚠️ 可选（图表功能）
│  └─ Qt Virtual Keyboard ❌ 不需要
├─ Qt Creator 4.15.x ✅ 推荐
├─ MinGW 8.1.0 ✅ 必需
├─ CMake 3.19+ ⚠️ 可选
└─ Ninja ⚠️ 可选
```

### 1.4 Dependencies工具准备

#### 🔧 Dependencies工具介绍

**Dependencies vs Dependency Walker**：
```
🆚 对比分析
┌─────────────────┬─────────────────┬─────────────────┐
│ 特性            │ Dependencies    │ Dependency Walker│
├─────────────────┼─────────────────┼─────────────────┤
│ Windows 11支持  │ ✅ 完美支持     │ ❌ 兼容性问题   │
│ 64位程序分析    │ ✅ 原生支持     │ ⚠️ 有限支持     │
│ 现代化界面      │ ✅ 现代化UI     │ ❌ 老旧界面     │
│ API Sets支持    │ ✅ 完整支持     │ ❌ 不支持       │
│ 更新维护        │ ✅ 活跃开发     │ ❌ 停止更新     │
│ 开源免费        │ ✅ MIT许可      │ ✅ 免费         │
└─────────────────┴─────────────────┴─────────────────┘
```

**Dependencies主要功能**：
- **DLL依赖分析**: 递归分析所有依赖关系
- **缺失库检测**: 自动识别缺失的DLL文件
- **版本冲突诊断**: 检测版本不匹配问题
- **API Sets支持**: 支持Windows 10/11的API Sets
- **导入/导出函数**: 详细的函数级别分析
- **现代化界面**: 支持高DPI和暗色主题

#### 📥 下载和基本配置

**官方下载地址**：
```
🌐 主要下载源
├─ GitHub官方: https://github.com/lucasg/Dependencies
├─ 发布页面: https://github.com/lucasg/Dependencies/releases
└─ 推荐版本: v1.11.1 或更新版本

📦 下载文件
├─ Dependencies_x64_Release.zip (64位推荐)
├─ Dependencies_x86_Release.zip (32位兼容)
└─ 文件大小: 约2-3MB
```

**安装配置步骤**：
```cmd
# 1. 创建工具目录
mkdir C:\Tools\Dependencies
cd C:\Tools\Dependencies

# 2. 解压下载的文件
# 将Dependencies_x64_Release.zip解压到当前目录

# 3. 验证安装
Dependencies.exe --version

# 4. 添加到系统PATH（可选）
setx PATH "%PATH%;C:\Tools\Dependencies"
```

**右键菜单集成**：
```batch
@echo off
REM 创建右键菜单集成脚本
echo 正在配置Dependencies右键菜单...

REM 创建注册表文件
echo Windows Registry Editor Version 5.00 > add_dependencies_menu.reg
echo. >> add_dependencies_menu.reg
echo [HKEY_CLASSES_ROOT\exefile\shell\Dependencies] >> add_dependencies_menu.reg
echo @="使用Dependencies分析" >> add_dependencies_menu.reg
echo. >> add_dependencies_menu.reg
echo [HKEY_CLASSES_ROOT\exefile\shell\Dependencies\command] >> add_dependencies_menu.reg
echo @="\"C:\\Tools\\Dependencies\\Dependencies.exe\" \"%%1\"" >> add_dependencies_menu.reg

REM 导入注册表
regedit /s add_dependencies_menu.reg

echo Dependencies右键菜单配置完成！
pause
```

#### 🔗 与Qt5分析的关联性

**Dependencies在Qt5开发中的作用**：
```
🎯 主要应用场景
├─ Qt5安装验证
│  ├─ 检查Qt5库文件完整性
│  ├─ 验证版本一致性
│  └─ 识别缺失组件
├─ ShengFan.exe依赖分析
│  ├─ 分析Qt5依赖关系
│  ├─ 检测DLL版本冲突
│  └─ 诊断运行时错误
├─ 部署环境检查
│  ├─ 确认运行时库完整
│  ├─ 验证目标机器兼容性
│  └─ 优化部署包大小
└─ 故障排除诊断
   ├─ 定位DLL加载失败
   ├─ 分析API调用问题
   └─ 解决版本冲突
```

**Qt5核心库分析要点**：
```
🔍 重点关注的Qt5依赖库
✅ Qt5Core.dll      - Qt基础核心功能
✅ Qt5Gui.dll       - 图形用户界面
✅ Qt5Widgets.dll   - 窗口控件系统
✅ Qt5Network.dll   - 网络通信功能
⭐ Qt5Mqtt.dll      - MQTT协议支持（关键）
✅ Qt5PrintSupport.dll - 打印功能支持
✅ Qt5Svg.dll       - SVG矢量图形

🔍 运行时库检查要点
✅ libgcc_s_dw2-1.dll    - GCC运行时库
✅ libstdc++-6.dll      - C++标准库
✅ libwinpthread-1.dll  - 线程支持库

🔍 状态指示器含义
🟢 绿色图标：DLL正常加载，无问题
🟡 黄色图标：存在警告，但不影响运行
🔴 红色图标：DLL缺失或严重错误
🔵 蓝色图标：系统DLL或API Sets
```

**Dependencies快速分析脚本**：
```batch
@echo off
REM Qt5项目Dependencies批量分析脚本
echo ================================
echo Qt5项目Dependencies分析工具
echo ================================

set DEPS_PATH=C:\Tools\Dependencies\Dependencies.exe
set TARGET_DIR=%~dp0

if not exist "%DEPS_PATH%" (
    echo ❌ 错误：未找到Dependencies工具
    echo 请确保Dependencies已安装到 C:\Tools\Dependencies\
    pause
    exit /b 1
)

echo 📂 分析目录: %TARGET_DIR%
echo.

REM 分析ShengFan.exe
if exist "%TARGET_DIR%ShengFan.exe" (
    echo 🔍 正在分析 ShengFan.exe...
    "%DEPS_PATH%" "%TARGET_DIR%ShengFan.exe"
    echo ✅ ShengFan.exe 分析完成
) else (
    echo ⚠️ 未找到 ShengFan.exe
)

echo.
echo 📋 分析完成！请查看Dependencies窗口中的结果
echo.
echo 🔍 重点检查项目：
echo   - Qt5*.dll 是否全部为绿色状态
echo   - 是否存在红色（缺失）的依赖库
echo   - 版本号是否一致
echo.
pause
```

**常见Qt5依赖问题诊断**：
```
❌ 常见问题及解决方案

问题1：Qt5Core.dll显示红色
原因：Qt5未正确安装或PATH环境变量未设置
解决：重新安装Qt5或配置环境变量

问题2：版本号不一致
原因：混合了不同版本的Qt5库
解决：清理旧版本，重新安装Qt5.15.2

问题3：libgcc_s_dw2-1.dll缺失
原因：MinGW运行时库未部署
解决：复制MinGW运行时库到程序目录

问题4：Qt5Mqtt.dll显示黄色警告
原因：MQTT模块版本不匹配
解决：确保使用Qt5.15.2完整安装包

问题5：API Sets相关错误
原因：Windows版本过低或系统文件损坏
解决：更新Windows或运行sfc /scannow
```

**Dependencies使用最佳实践**：
```
📋 分析流程建议
1️⃣ 环境准备
   - 确保Dependencies已正确安装
   - 配置右键菜单快捷方式
   - 准备分析目标文件

2️⃣ 基础分析
   - 打开ShengFan.exe进行分析
   - 检查依赖关系树的完整性
   - 记录所有红色和黄色警告

3️⃣ 深入诊断
   - 逐个检查Qt5核心库状态
   - 验证版本号一致性
   - 分析运行时库依赖

4️⃣ 问题解决
   - 根据分析结果定位问题
   - 参考故障排除指南
   - 验证修复效果

5️⃣ 文档记录
   - 记录分析结果和解决方案
   - 建立问题知识库
   - 为团队提供参考
```

---

## 📝 第一部分总结

### ✅ 完成的准备工作

通过第一部分的学习，您应该已经完成了以下准备工作：

1. **系统环境检查** 🟢
   - ✅ 确认Windows 10/11系统兼容性
   - ✅ 验证硬件配置满足要求
   - ✅ 测试网络连接稳定性

2. **环境检查工具** 🟢
   - ✅ 掌握系统信息查看方法
   - ✅ 学会已安装软件检查
   - ✅ 了解注册表检查脚本

3. **Qt5版本选择** 🟢
   - ✅ 理解Qt5.15.2 LTS的优势
   - ✅ 掌握版本兼容性分析方法
   - ✅ 确认与ShengFan.exe的匹配性

4. **Dependencies工具** 🟢
   - ✅ 完成Dependencies工具下载安装
   - ✅ 配置右键菜单快捷方式
   - ✅ 理解与Qt5分析的关联性

### 🎯 下一步行动

现在您已经完成了基础准备工作，可以继续进行：
- **第二部分：安装配置** 🟡 - Qt5的获取、安装和环境配置
- **第三部分：开发环境** 🟡 - Qt Creator IDE和编译器设置
- **第四部分：集成应用** 🔴 - 高级集成和实际案例演示

---

# 第二部分：安装配置 🟡

## 2. Qt5安装与配置 🟡

### 2.1 Qt5获取方式

#### 🌐 官方在线安装器使用方法

**Qt Online Installer优势**：
```
✅ 在线安装器优点
├─ 自动获取最新版本
├─ 智能组件选择
├─ 增量更新支持
├─ 官方数字签名验证
└─ 自动依赖解析

⚠️ 注意事项
├─ 需要稳定网络连接
├─ 下载速度依赖网络
├─ 需要Qt账户登录
└─ 某些地区访问较慢
```

**下载官方在线安装器**：
```
🔗 官方下载地址
├─ 主站: https://www.qt.io/download-qt-installer
├─ 直链: https://download.qt.io/official_releases/online_installers/
├─ 文件名: qt-unified-windows-x64-4.6.1-online.exe
└─ 文件大小: 约30MB

📋 系统要求
├─ Windows 10/11 64位
├─ 管理员权限
├─ 稳定网络连接
└─ 至少15GB可用空间
```

**在线安装器使用步骤**：
```
📝 详细安装流程
1️⃣ 下载并启动安装器
   - 右键"以管理员身份运行"
   - 等待初始化完成

2️⃣ 登录Qt账户
   - 使用现有账户或注册新账户
   - 选择"Open Source"免费版本

3️⃣ 选择安装路径
   - 推荐路径: C:\Qt
   - 确保有足够磁盘空间

4️⃣ 组件选择（重要）
   - Qt 5.15.2 LTS
   - MinGW 8.1.0 64-bit
   - Qt Creator 4.15.x
   - Qt5Mqtt模块

5️⃣ 开始安装
   - 确认选择并开始下载
   - 等待安装完成（约30-60分钟）
```

#### 📦 离线安装包下载和使用

**离线安装包优势**：
```
✅ 离线安装优点
├─ 无需网络连接
├─ 安装速度快
├─ 可重复使用
├─ 版本固定稳定
└─ 适合批量部署

⚠️ 注意事项
├─ 文件体积较大（3-5GB）
├─ 需要手动更新
├─ 组件选择有限
└─ 下载时间较长
```

**官方离线包下载地址**：
```
🔗 Qt5.15.2 LTS离线包
├─ 官方FTP: https://download.qt.io/archive/qt/5.15/5.15.2/
├─ 推荐文件: qt-opensource-windows-x86-5.15.2.exe
├─ 文件大小: 约4.2GB
└─ MD5校验: 建议下载后验证

📋 组件说明
├─ qt-opensource-windows-x86-5.15.2.exe (完整版)
├─ 包含Qt Creator IDE
├─ 包含MinGW 8.1.0编译器
├─ 包含所有标准模块
└─ 包含Qt5Mqtt模块
```

**离线安装包使用方法**：
```
📝 离线安装流程
1️⃣ 下载验证
   # PowerShell验证MD5
   Get-FileHash qt-opensource-windows-x86-5.15.2.exe -Algorithm MD5

2️⃣ 启动安装
   - 右键"以管理员身份运行"
   - 跳过在线账户登录

3️⃣ 许可协议
   - 选择"Open Source"
   - 接受GPL v3许可协议

4️⃣ 安装路径设置
   - 默认: C:\Qt\Qt5.15.2
   - 自定义: 确保路径无中文和空格

5️⃣ 组件选择
   - 保持默认选择
   - 确保包含MinGW 8.1.0
   - 确保包含Qt Creator

6️⃣ 完成安装
   - 等待文件复制完成
   - 配置开始菜单快捷方式
```

#### 🇨🇳 国内镜像站点推荐

**清华大学镜像站**：
```
🔗 清华大学开源软件镜像站
├─ 主页: https://mirrors.tuna.tsinghua.edu.cn/qt/
├─ Qt5.15.2: https://mirrors.tuna.tsinghua.edu.cn/qt/archive/qt/5.15/5.15.2/
├─ 在线安装器: https://mirrors.tuna.tsinghua.edu.cn/qt/official_releases/online_installers/
└─ 更新频率: 每日同步

📊 速度测试
├─ 国内访问速度: ⭐⭐⭐⭐⭐
├─ 稳定性: ⭐⭐⭐⭐⭐
├─ 完整性: ⭐⭐⭐⭐⭐
└─ 推荐指数: ⭐⭐⭐⭐⭐
```

**中科大镜像站**：
```
🔗 中科大开源软件镜像站
├─ 主页: https://mirrors.ustc.edu.cn/qtproject/
├─ Qt5.15.2: https://mirrors.ustc.edu.cn/qtproject/archive/qt/5.15/5.15.2/
├─ 在线安装器: https://mirrors.ustc.edu.cn/qtproject/official_releases/online_installers/
└─ 更新频率: 每日同步

📊 速度测试
├─ 国内访问速度: ⭐⭐⭐⭐⭐
├─ 稳定性: ⭐⭐⭐⭐
├─ 完整性: ⭐⭐⭐⭐⭐
└─ 推荐指数: ⭐⭐⭐⭐
```

**配置镜像源方法**：
```cmd
REM 配置Qt在线安装器使用国内镜像
REM 方法1：命令行参数
qt-unified-windows-x64-online.exe --mirror https://mirrors.tuna.tsinghua.edu.cn/qt

REM 方法2：环境变量设置
set QT_MIRROR_URL=https://mirrors.tuna.tsinghua.edu.cn/qt
qt-unified-windows-x64-online.exe

REM 方法3：配置文件修改（高级用户）
REM 编辑 %APPDATA%\QtProject\qtcreator\QtProject.conf
REM 添加: MirrorUrl=https://mirrors.tuna.tsinghua.edu.cn/qt
```

#### 📊 各种方式的优缺点对比

**安装方式对比表**：
| 安装方式 | 网络要求 | 安装速度 | 文件大小 | 更新便利性 | 推荐场景 | 推荐度 |
|---------|----------|----------|----------|------------|----------|--------|
| **在线安装器** | 高速稳定网络 | 中等 | 30MB | ⭐⭐⭐⭐⭐ | 个人开发 | ⭐⭐⭐⭐ |
| **离线安装包** | 仅下载时需要 | 快速 | 4.2GB | ⭐⭐ | 批量部署 | ⭐⭐⭐⭐⭐ |
| **清华镜像** | 中速网络 | 快速 | 30MB/4.2GB | ⭐⭐⭐⭐ | 国内用户 | ⭐⭐⭐⭐⭐ |
| **中科大镜像** | 中速网络 | 快速 | 30MB/4.2GB | ⭐⭐⭐⭐ | 国内用户 | ⭐⭐⭐⭐ |

**选择建议**：
```
🎯 推荐选择策略

情况1：首次安装 + 国内用户
推荐：清华镜像 + 离线安装包
理由：下载速度快，安装稳定

情况2：已有Qt经验 + 需要定制
推荐：官方在线安装器
理由：组件选择灵活，更新方便

情况3：企业批量部署
推荐：离线安装包
理由：统一版本，无网络依赖

情况4：网络环境不稳定
推荐：离线安装包
理由：一次下载，多次使用

情况5：学习测试用途
推荐：清华镜像 + 在线安装器
理由：快速获取，便于更新
```

### 2.2 详细安装步骤

#### 🔧 在线安装器完整流程

**步骤1：准备工作**
```powershell
# 安装前环境检查脚本
function Pre-InstallCheck {
    Write-Host "=== Qt5安装前检查 ===" -ForegroundColor Cyan

    # 检查管理员权限
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-Host "❌ 需要管理员权限运行" -ForegroundColor Red
        return $false
    }

    # 检查磁盘空间
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}).FreeSpace / 1GB
    if ($freeSpace -lt 20) {
        Write-Host "❌ C盘空间不足，需要至少20GB" -ForegroundColor Red
        return $false
    }

    # 检查网络连接
    try {
        Test-NetConnection -ComputerName "download.qt.io" -Port 443 -InformationLevel Quiet
        Write-Host "✅ 网络连接正常" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 网络连接异常，建议使用离线安装包" -ForegroundColor Yellow
    }

    Write-Host "✅ 安装前检查完成" -ForegroundColor Green
    return $true
}

Pre-InstallCheck
```

**步骤2：下载并启动安装器**
```cmd
REM 下载在线安装器
curl -L -o qt-unified-windows-x64-online.exe https://download.qt.io/official_releases/online_installers/qt-unified-windows-x64-online.exe

REM 验证文件完整性
certutil -hashfile qt-unified-windows-x64-online.exe SHA256

REM 以管理员身份启动
powershell -Command "Start-Process qt-unified-windows-x64-online.exe -Verb RunAs"
```

**步骤3：安装器界面操作**
```
📱 界面操作指南

🔹 欢迎界面
   - 点击"Next"继续
   - 阅读并接受许可协议

🔹 Qt账户登录
   - 输入Qt账户邮箱和密码
   - 或点击"Skip"跳过（功能受限）
   - 选择"Open Source"使用免费版

🔹 安装路径选择
   - 默认路径：C:\Qt
   - 自定义路径：避免中文和空格
   - 确保目标磁盘有足够空间

🔹 组件选择（关键步骤）
   ✅ Qt 5.15.2 LTS
      ├─ MinGW 8.1.0 64-bit ✅ 必选
      ├─ MSVC 2019 64-bit ⚠️ 可选
      ├─ Qt5Mqtt ✅ 必选
      ├─ Qt Charts ⚠️ 可选
      └─ Sources ❌ 不需要
   ✅ Qt Creator 4.15.x ✅ 推荐
   ✅ MinGW 8.1.0 ✅ 必选
   ❌ Qt Design Studio ❌ 不需要

🔹 开始安装
   - 确认组件选择
   - 点击"Install"开始下载
   - 等待安装完成（30-60分钟）
```

**步骤4：安装过程监控**
```powershell
# 安装过程监控脚本
function Monitor-QtInstallation {
    $qtPath = "C:\Qt"
    $startTime = Get-Date

    Write-Host "=== Qt5安装进度监控 ===" -ForegroundColor Cyan
    Write-Host "开始时间: $startTime" -ForegroundColor Yellow

    while ($true) {
        if (Test-Path "$qtPath\5.15.2") {
            $currentSize = (Get-ChildItem -Path "$qtPath\5.15.2" -Recurse | Measure-Object -Property Length -Sum).Sum / 1GB
            $elapsed = (Get-Date) - $startTime

            Write-Host "已安装: $([math]::Round($currentSize, 2))GB | 耗时: $($elapsed.ToString('hh\:mm\:ss'))" -ForegroundColor Green

            # 检查关键文件
            $qtCore = Test-Path "$qtPath\5.15.2\mingw81_64\bin\Qt5Core.dll"
            $qtCreator = Test-Path "$qtPath\Tools\QtCreator\bin\qtcreator.exe"

            if ($qtCore -and $qtCreator) {
                Write-Host "✅ 安装完成！" -ForegroundColor Green
                break
            }
        }

        Start-Sleep -Seconds 30
    }
}

# 在另一个PowerShell窗口中运行监控
# Monitor-QtInstallation
```

#### 📦 离线安装包安装流程

**步骤1：下载离线安装包**
```powershell
# 离线包下载脚本
function Download-QtOfflineInstaller {
    $url = "https://mirrors.tuna.tsinghua.edu.cn/qt/archive/qt/5.15/5.15.2/qt-opensource-windows-x86-5.15.2.exe"
    $output = "qt-opensource-windows-x86-5.15.2.exe"

    Write-Host "=== 下载Qt5.15.2离线安装包 ===" -ForegroundColor Cyan
    Write-Host "下载地址: $url" -ForegroundColor Yellow
    Write-Host "保存位置: $output" -ForegroundColor Yellow

    # 使用Invoke-WebRequest下载
    try {
        $progressPreference = 'Continue'
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing

        # 验证文件大小
        $fileSize = (Get-Item $output).Length / 1GB
        Write-Host "下载完成，文件大小: $([math]::Round($fileSize, 2))GB" -ForegroundColor Green

        # 验证文件完整性
        $hash = Get-FileHash $output -Algorithm SHA256
        Write-Host "SHA256: $($hash.Hash)" -ForegroundColor Yellow

    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Download-QtOfflineInstaller
```

**步骤2：离线安装执行**
```cmd
REM 离线安装命令
REM 静默安装（高级用户）
qt-opensource-windows-x86-5.15.2.exe --script install_script.qs

REM 图形界面安装（推荐）
qt-opensource-windows-x86-5.15.2.exe
```

**步骤3：离线安装脚本**
```javascript
// install_script.qs - Qt静默安装脚本
function Controller() {
    installer.autoRejectMessageBoxes();
    installer.installationFinished.connect(function() {
        gui.clickButton(buttons.NextButton);
    })
}

Controller.prototype.WelcomePageCallback = function() {
    gui.clickButton(buttons.NextButton, 3000);
}

Controller.prototype.CredentialsPageCallback = function() {
    gui.clickButton(buttons.SkipButton);
}

Controller.prototype.IntroductionPageCallback = function() {
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.TargetDirectoryPageCallback = function() {
    gui.currentPageWidget().TargetDirectoryLineEdit.setText("C:\\Qt");
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.ComponentSelectionPageCallback = function() {
    var widget = gui.currentPageWidget();

    // 选择Qt 5.15.2组件
    widget.selectComponent("qt.qt5.5152.win64_mingw81");
    widget.selectComponent("qt.qt5.5152.qtmqtt");

    // 选择工具
    widget.selectComponent("qt.tools.qtcreator");
    widget.selectComponent("qt.tools.mingw810_64");

    gui.clickButton(buttons.NextButton);
}

Controller.prototype.LicenseAgreementPageCallback = function() {
    gui.currentPageWidget().AcceptLicenseRadioButton.setChecked(true);
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.StartMenuDirectoryPageCallback = function() {
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.ReadyForInstallationPageCallback = function() {
    gui.clickButton(buttons.InstallButton);
}

Controller.prototype.FinishedPageCallback = function() {
    var checkBoxForm = gui.currentPageWidget().LaunchQtCreatorCheckBoxForm;
    if (checkBoxForm && checkBoxForm.launchQtCreatorCheckBox) {
        checkBoxForm.launchQtCreatorCheckBox.checked = false;
    }
    gui.clickButton(buttons.FinishButton);
}
```

#### 🎯 组件选择建议

**必需组件清单**：
```
✅ 核心必需组件
├─ Qt 5.15.2 LTS
│  ├─ MinGW 8.1.0 64-bit ✅ 编译器工具链
│  ├─ Qt5Mqtt ✅ MQTT协议支持（ShengFan.exe必需）
│  ├─ Qt Charts ⚠️ 图表功能（可选）
│  └─ Sources ❌ 源代码（不推荐，占用空间大）
├─ Qt Creator 4.15.x ✅ 集成开发环境
├─ MinGW 8.1.0 ✅ 编译器
└─ CMake 3.19+ ⚠️ 构建工具（可选）

❌ 不推荐组件
├─ Qt Design Studio ❌ 设计工具（占用空间大）
├─ Qt Quick 3D ❌ 3D功能（ShengFan.exe不需要）
├─ Qt WebEngine ❌ 浏览器引擎（体积大）
└─ Android/iOS支持 ❌ 移动平台（不需要）
```

**组件大小参考**：
```
📊 磁盘空间占用
├─ Qt 5.15.2 MinGW 64-bit: ~2.1GB
├─ Qt5Mqtt模块: ~15MB
├─ Qt Creator: ~400MB
├─ MinGW 8.1.0: ~1.2GB
├─ Qt Charts: ~50MB
├─ 文档和示例: ~800MB
└─ 总计（推荐配置）: ~4.5GB
```

#### 📁 安装路径规划

**推荐路径结构**：
```
📂 C:\Qt\ (推荐根目录)
├─ 5.15.2\                    # Qt版本目录
│  ├─ mingw81_64\             # MinGW 64位编译器版本
│  │  ├─ bin\                 # 可执行文件和DLL
│  │  ├─ include\             # 头文件
│  │  ├─ lib\                 # 库文件
│  │  └─ plugins\             # 插件
│  └─ Src\                    # 源代码（可选）
├─ Tools\                     # 开发工具
│  ├─ QtCreator\              # Qt Creator IDE
│  ├─ mingw810_64\            # MinGW编译器
│  └─ CMake_64\               # CMake构建工具
└─ Docs\                      # 文档（可选）
```

**路径选择原则**：
```
✅ 推荐做法
├─ 使用默认路径 C:\Qt
├─ 避免包含中文字符
├─ 避免包含空格
├─ 选择SSD磁盘（提升性能）
└─ 确保有足够空间（20GB+）

❌ 避免的路径
├─ C:\Program Files\ (权限问题)
├─ C:\用户\文档\ (中文路径)
├─ D:\My Qt Files\ (包含空格)
├─ 网络驱动器 (性能问题)
└─ 临时目录 (可能被清理)
```

**自定义路径配置**：
```powershell
# 自定义安装路径验证脚本
function Test-QtInstallPath {
    param(
        [string]$Path = "C:\Qt"
    )

    Write-Host "=== Qt安装路径验证 ===" -ForegroundColor Cyan
    Write-Host "目标路径: $Path" -ForegroundColor Yellow

    # 检查路径是否包含中文
    if ($Path -match '[\u4e00-\u9fa5]') {
        Write-Host "❌ 路径包含中文字符，可能导致编译问题" -ForegroundColor Red
        return $false
    }

    # 检查路径是否包含空格
    if ($Path -match '\s') {
        Write-Host "❌ 路径包含空格，可能导致构建问题" -ForegroundColor Red
        return $false
    }

    # 检查磁盘空间
    $drive = Split-Path -Qualifier $Path
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq $drive}).FreeSpace / 1GB

    if ($freeSpace -lt 20) {
        Write-Host "❌ 磁盘空间不足，需要至少20GB，当前可用: $([math]::Round($freeSpace, 2))GB" -ForegroundColor Red
        return $false
    }

    # 检查写入权限
    try {
        $testFile = Join-Path $Path "test_write_permission.tmp"
        New-Item -Path $testFile -ItemType File -Force | Out-Null
        Remove-Item -Path $testFile -Force
        Write-Host "✅ 写入权限正常" -ForegroundColor Green
    } catch {
        Write-Host "❌ 写入权限不足，请以管理员身份运行" -ForegroundColor Red
        return $false
    }

    Write-Host "✅ 路径验证通过" -ForegroundColor Green
    return $true
}

# 使用示例
Test-QtInstallPath -Path "C:\Qt"
```

### 2.3 环境变量配置

#### 🔧 Qt5路径添加到PATH

**自动配置脚本**：
```powershell
# Qt5环境变量自动配置脚本
function Set-QtEnvironment {
    param(
        [string]$QtPath = "C:\Qt\5.15.2\mingw81_64",
        [string]$QtToolsPath = "C:\Qt\Tools\mingw810_64"
    )

    Write-Host "=== Qt5环境变量配置 ===" -ForegroundColor Cyan

    # 验证路径存在
    if (-not (Test-Path $QtPath)) {
        Write-Host "❌ Qt路径不存在: $QtPath" -ForegroundColor Red
        return $false
    }

    if (-not (Test-Path $QtToolsPath)) {
        Write-Host "❌ Qt工具路径不存在: $QtToolsPath" -ForegroundColor Red
        return $false
    }

    # 获取当前PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

    # 要添加的路径
    $pathsToAdd = @(
        "$QtPath\bin",
        "$QtToolsPath\bin"
    )

    $pathsAdded = @()

    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$pathToAdd;$currentPath"
            $pathsAdded += $pathToAdd
            Write-Host "✅ 添加路径: $pathToAdd" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 路径已存在: $pathToAdd" -ForegroundColor Yellow
        }
    }

    if ($pathsAdded.Count -gt 0) {
        # 设置用户环境变量
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, "User")
        Write-Host "✅ PATH环境变量已更新" -ForegroundColor Green
        Write-Host "⚠️ 请重启命令行窗口使环境变量生效" -ForegroundColor Yellow
    }

    return $true
}

Set-QtEnvironment
```

**手动配置方法**：
```cmd
REM 方法1：使用setx命令（永久设置）
setx PATH "%PATH%;C:\Qt\5.15.2\mingw81_64\bin;C:\Qt\Tools\mingw810_64\bin"

REM 方法2：使用set命令（临时设置）
set PATH=%PATH%;C:\Qt\5.15.2\mingw81_64\bin;C:\Qt\Tools\mingw810_64\bin

REM 方法3：图形界面设置
REM 1. Win+R 输入 sysdm.cpl
REM 2. 高级 → 环境变量
REM 3. 用户变量 → PATH → 编辑
REM 4. 新建 → 添加Qt路径
```

#### 🎯 QTDIR环境变量设置

**QTDIR变量配置**：
```powershell
# QTDIR环境变量设置
function Set-QtDirEnvironment {
    param(
        [string]$QtDir = "C:\Qt\5.15.2\mingw81_64"
    )

    Write-Host "=== QTDIR环境变量设置 ===" -ForegroundColor Cyan

    if (-not (Test-Path $QtDir)) {
        Write-Host "❌ Qt目录不存在: $QtDir" -ForegroundColor Red
        return $false
    }

    # 设置QTDIR环境变量
    [Environment]::SetEnvironmentVariable("QTDIR", $QtDir, "User")
    Write-Host "✅ QTDIR设置为: $QtDir" -ForegroundColor Green

    # 设置Qt5_DIR（CMake使用）
    [Environment]::SetEnvironmentVariable("Qt5_DIR", "$QtDir\lib\cmake\Qt5", "User")
    Write-Host "✅ Qt5_DIR设置为: $QtDir\lib\cmake\Qt5" -ForegroundColor Green

    # 验证设置
    $qtdir = [Environment]::GetEnvironmentVariable("QTDIR", "User")
    $qt5dir = [Environment]::GetEnvironmentVariable("Qt5_DIR", "User")

    Write-Host "当前QTDIR: $qtdir" -ForegroundColor Yellow
    Write-Host "当前Qt5_DIR: $qt5dir" -ForegroundColor Yellow

    return $true
}

Set-QtDirEnvironment
```

**批处理配置脚本**：
```batch
@echo off
REM Qt5环境变量批量配置脚本
echo ================================
echo Qt5环境变量配置工具
echo ================================

set QT_ROOT=C:\Qt\5.15.2\mingw81_64
set QT_TOOLS=C:\Qt\Tools\mingw810_64

echo 检查Qt安装路径...
if not exist "%QT_ROOT%\bin\qmake.exe" (
    echo ❌ 错误：未找到qmake.exe，请检查Qt安装路径
    pause
    exit /b 1
)

echo ✅ Qt安装路径验证成功

echo 设置环境变量...
setx QTDIR "%QT_ROOT%"
setx Qt5_DIR "%QT_ROOT%\lib\cmake\Qt5"
setx PATH "%PATH%;%QT_ROOT%\bin;%QT_TOOLS%\bin"

echo ✅ 环境变量设置完成
echo.
echo 📋 设置的环境变量：
echo   QTDIR = %QT_ROOT%
echo   Qt5_DIR = %QT_ROOT%\lib\cmake\Qt5
echo   PATH = 已添加Qt相关路径
echo.
echo ⚠️ 请重启命令行窗口使环境变量生效
pause
```

#### ✅ 验证环境变量配置

**环境变量验证脚本**：
```powershell
# Qt5环境变量验证脚本
function Test-QtEnvironment {
    Write-Host "=== Qt5环境变量验证 ===" -ForegroundColor Cyan

    $allPassed = $true

    # 检查QTDIR
    $qtdir = [Environment]::GetEnvironmentVariable("QTDIR", "User")
    if ($qtdir -and (Test-Path $qtdir)) {
        Write-Host "✅ QTDIR: $qtdir" -ForegroundColor Green
    } else {
        Write-Host "❌ QTDIR未设置或路径无效" -ForegroundColor Red
        $allPassed = $false
    }

    # 检查Qt5_DIR
    $qt5dir = [Environment]::GetEnvironmentVariable("Qt5_DIR", "User")
    if ($qt5dir -and (Test-Path $qt5dir)) {
        Write-Host "✅ Qt5_DIR: $qt5dir" -ForegroundColor Green
    } else {
        Write-Host "❌ Qt5_DIR未设置或路径无效" -ForegroundColor Red
        $allPassed = $false
    }

    # 检查PATH中的Qt路径
    $path = [Environment]::GetEnvironmentVariable("PATH", "User")
    $qtPaths = @("Qt", "mingw")
    $foundPaths = @()

    foreach ($qtPath in $qtPaths) {
        if ($path -like "*$qtPath*") {
            $foundPaths += $qtPath
        }
    }

    if ($foundPaths.Count -gt 0) {
        Write-Host "✅ PATH包含Qt相关路径: $($foundPaths -join ', ')" -ForegroundColor Green
    } else {
        Write-Host "❌ PATH中未找到Qt相关路径" -ForegroundColor Red
        $allPassed = $false
    }

    # 测试命令可用性
    Write-Host "`n命令可用性测试:" -ForegroundColor Yellow

    $commands = @("qmake", "gcc", "g++", "mingw32-make")
    foreach ($cmd in $commands) {
        try {
            $null = Get-Command $cmd -ErrorAction Stop
            Write-Host "✅ $cmd 可用" -ForegroundColor Green
        } catch {
            Write-Host "❌ $cmd 不可用" -ForegroundColor Red
            $allPassed = $false
        }
    }

    if ($allPassed) {
        Write-Host "`n🎉 环境变量配置验证通过！" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️ 环境变量配置存在问题，请检查上述错误" -ForegroundColor Yellow
    }

    return $allPassed
}

Test-QtEnvironment
```

### 2.4 安装验证

#### 🔍 Qt5库文件检查

**核心库文件验证**：
```powershell
# Qt5核心库文件检查脚本
function Test-QtLibraries {
    param(
        [string]$QtPath = "C:\Qt\5.15.2\mingw81_64"
    )

    Write-Host "=== Qt5库文件完整性检查 ===" -ForegroundColor Cyan

    # 核心库文件列表
    $coreLibraries = @(
        "bin\Qt5Core.dll",
        "bin\Qt5Gui.dll",
        "bin\Qt5Widgets.dll",
        "bin\Qt5Network.dll",
        "bin\Qt5Mqtt.dll",
        "bin\Qt5PrintSupport.dll",
        "bin\Qt5Svg.dll"
    )

    # 运行时库文件
    $runtimeLibraries = @(
        "bin\libgcc_s_dw2-1.dll",
        "bin\libstdc++-6.dll",
        "bin\libwinpthread-1.dll"
    )

    # 开发工具
    $developmentTools = @(
        "bin\qmake.exe",
        "bin\moc.exe",
        "bin\uic.exe",
        "bin\rcc.exe"
    )

    $allFiles = $coreLibraries + $runtimeLibraries + $developmentTools
    $missingFiles = @()
    $foundFiles = @()

    Write-Host "检查路径: $QtPath" -ForegroundColor Yellow

    foreach ($file in $allFiles) {
        $fullPath = Join-Path $QtPath $file
        if (Test-Path $fullPath) {
            $version = (Get-ItemProperty $fullPath).VersionInfo.FileVersion
            Write-Host "✅ $file - 版本: $version" -ForegroundColor Green
            $foundFiles += $file
        } else {
            Write-Host "❌ $file - 缺失" -ForegroundColor Red
            $missingFiles += $file
        }
    }

    # 统计结果
    Write-Host "`n📊 检查结果统计:" -ForegroundColor Cyan
    Write-Host "  找到文件: $($foundFiles.Count)/$($allFiles.Count)" -ForegroundColor Green
    Write-Host "  缺失文件: $($missingFiles.Count)" -ForegroundColor Red

    if ($missingFiles.Count -eq 0) {
        Write-Host "🎉 所有必需文件检查通过！" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️ 发现缺失文件，建议重新安装Qt5" -ForegroundColor Yellow
        return $false
    }
}

Test-QtLibraries
```

#### 📋 版本信息验证

**版本一致性检查**：
```powershell
# Qt5版本信息验证脚本
function Test-QtVersion {
    Write-Host "=== Qt5版本信息验证 ===" -ForegroundColor Cyan

    # 检查qmake版本
    try {
        $qmakeVersion = qmake -version 2>&1
        Write-Host "qmake版本信息:" -ForegroundColor Yellow
        Write-Host $qmakeVersion -ForegroundColor White

        # 提取Qt版本号
        if ($qmakeVersion -match "Qt version (\d+\.\d+\.\d+)") {
            $qtVersion = $matches[1]
            if ($qtVersion -eq "5.15.2") {
                Write-Host "✅ Qt版本正确: $qtVersion" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Qt版本不匹配，期望5.15.2，实际: $qtVersion" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "❌ 无法获取qmake版本信息" -ForegroundColor Red
        return $false
    }

    # 检查编译器版本
    try {
        $gccVersion = gcc --version 2>&1 | Select-Object -First 1
        Write-Host "`n编译器版本:" -ForegroundColor Yellow
        Write-Host $gccVersion -ForegroundColor White

        if ($gccVersion -match "8\.1\.0") {
            Write-Host "✅ MinGW版本正确: 8.1.0" -ForegroundColor Green
        } else {
            Write-Host "⚠️ MinGW版本可能不匹配" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 无法获取编译器版本信息" -ForegroundColor Red
        return $false
    }

    # 检查Qt Creator版本
    $qtCreatorPath = "C:\Qt\Tools\QtCreator\bin\qtcreator.exe"
    if (Test-Path $qtCreatorPath) {
        $qtCreatorVersion = (Get-ItemProperty $qtCreatorPath).VersionInfo.FileVersion
        Write-Host "`nQt Creator版本: $qtCreatorVersion" -ForegroundColor Yellow
        Write-Host "✅ Qt Creator已安装" -ForegroundColor Green
    } else {
        Write-Host "❌ Qt Creator未找到" -ForegroundColor Red
    }

    return $true
}

Test-QtVersion
```

#### 🧪 基本功能测试

**Hello World测试项目**：
```cpp
// hello_qt.cpp - Qt5基本功能测试
#include <QApplication>
#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QMessageBox>
#include <QtMqtt/QMqttClient>

class TestWidget : public QWidget {
    Q_OBJECT

public:
    TestWidget(QWidget *parent = nullptr) : QWidget(parent) {
        setupUI();
        testMqtt();
    }

private slots:
    void showTestResult() {
        QMessageBox::information(this, "Qt5测试",
            "Qt5安装验证成功！\n"
            "✅ Qt5Core - 正常\n"
            "✅ Qt5Gui - 正常\n"
            "✅ Qt5Widgets - 正常\n"
            "✅ Qt5Mqtt - 正常");
    }

private:
    void setupUI() {
        setWindowTitle("Qt5安装验证测试");
        setFixedSize(300, 200);

        auto *layout = new QVBoxLayout(this);
        auto *label = new QLabel("Qt5安装验证测试", this);
        label->setAlignment(Qt::AlignCenter);

        auto *button = new QPushButton("测试Qt5功能", this);
        connect(button, &QPushButton::clicked, this, &TestWidget::showTestResult);

        layout->addWidget(label);
        layout->addWidget(button);
    }

    void testMqtt() {
        // 测试MQTT模块是否可用
        QMqttClient *client = new QMqttClient(this);
        client->setHostname("test.mosquitto.org");
        client->setPort(1883);
        // 不实际连接，只测试模块加载
    }
};

#include "hello_qt.moc"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    TestWidget widget;
    widget.show();

    return app.exec();
}
```

**测试项目构建脚本**：
```batch
@echo off
REM Qt5功能测试项目构建脚本
echo ================================
echo Qt5功能测试项目构建
echo ================================

REM 创建测试目录
mkdir qt5_test 2>nul
cd qt5_test

REM 创建测试源文件
echo 创建测试源文件...
(
echo #include ^<QApplication^>
echo #include ^<QWidget^>
echo #include ^<QLabel^>
echo #include ^<QVBoxLayout^>
echo #include ^<QPushButton^>
echo #include ^<QMessageBox^>
echo.
echo int main^(int argc, char *argv[]^) {
echo     QApplication app^(argc, argv^);
echo     QWidget window;
echo     window.setWindowTitle^("Qt5安装验证"^);
echo     window.setFixedSize^(300, 150^);
echo.
echo     QVBoxLayout *layout = new QVBoxLayout^(&window^);
echo     QLabel *label = new QLabel^("Qt5安装验证成功！"^);
echo     label-^>setAlignment^(Qt::AlignCenter^);
echo     layout-^>addWidget^(label^);
echo.
echo     window.show^(^);
echo     return app.exec^(^);
echo }
) > main.cpp

REM 创建项目文件
echo 创建项目文件...
(
echo QT += core gui widgets
echo CONFIG += c++11
echo TARGET = qt5_test
echo SOURCES += main.cpp
) > qt5_test.pro

REM 生成Makefile
echo 生成Makefile...
qmake qt5_test.pro

REM 编译项目
echo 编译项目...
mingw32-make

REM 检查编译结果
if exist "release\qt5_test.exe" (
    echo ✅ 编译成功！
    echo 🚀 启动测试程序...
    start release\qt5_test.exe
) else (
    echo ❌ 编译失败，请检查Qt5安装
)

pause
```

---

## 📝 第二部分总结

### ✅ 完成的安装配置

通过第二部分的学习，您应该已经完成了以下安装配置：

1. **Qt5获取方式** 🟢
   - ✅ 了解官方在线安装器使用方法
   - ✅ 掌握离线安装包下载和使用
   - ✅ 配置国内镜像站点加速下载
   - ✅ 理解各种方式的优缺点对比

2. **详细安装步骤** 🟢
   - ✅ 完成在线安装器完整流程
   - ✅ 掌握离线安装包安装流程
   - ✅ 学会组件选择和安装路径规划
   - ✅ 了解静默安装脚本使用

3. **环境变量配置** 🟢
   - ✅ 正确添加Qt5路径到PATH
   - ✅ 设置QTDIR和Qt5_DIR环境变量
   - ✅ 验证环境变量配置正确性

4. **安装验证** 🟢
   - ✅ 检查Qt5库文件完整性
   - ✅ 验证版本信息一致性
   - ✅ 完成基本功能测试

### 🎯 下一步行动

现在您已经完成了Qt5的安装配置，可以继续进行：
- **第三部分：开发环境** 🟡 - Qt Creator IDE和编译器详细设置
- **第四部分：集成应用** 🔴 - 高级集成和实际案例演示
- **第五部分：附录资源** 🔴 - 故障排除和参考资料

---

*文档创建时间: 2025年6月29日*
*最后更新: 2025年6月29日*
*文档版本: v1.0.0*
