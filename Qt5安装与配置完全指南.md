# Qt5安装与配置完全指南

## 文档信息

- **文档标题**: Qt5安装与配置完全指南
- **版本**: v1.0.0
- **创建日期**: 2025年6月29日
- **最后更新**: 2025年6月29日
- **适用软件**: Qt5框架 + ShengFan.exe v1.0
- **编写者**: 技术文档团队
- **文档状态**: 正式发布版
- **页数**: 约80页
- **字数**: 约40,000字
- **专注平台**: Qt5.15.2 LTS + OneNET云平台 + Dependencies工具

## 适用范围

本文档适用于以下用户群体：
- **有编程基础的用户**: 具备基本的C++或Qt开发经验的技术人员
- **上位机开发者**: 需要搭建Qt5开发环境的工程师
- **ShengFan.exe用户**: 需要运行或开发智能电表上位机软件的专业人员
- **学习者**: 希望学习Qt5安装配置的学生和开发者

## 前置条件

使用本文档前，请确保满足以下条件：
- Windows 10/11 操作系统（64位推荐）
- 具备基本的编程知识（C++基础）
- 了解基础的软件安装和配置概念
- 具备基本的命令行操作能力
- 网络连接（用于下载安装包和在线资源）
- 至少8GB内存和20GB可用磁盘空间

## 文档使用说明

### 📖 阅读指南

本文档采用分层次设计，您可以根据自己的技术水平和需求选择相应的章节：

- **🟢 基础准备**: 标有绿色图标，适合初学者，包含系统要求和环境准备
- **🟡 安装配置**: 标有黄色图标，需要一定技术基础，包含Qt5安装和环境配置
- **🟡 开发环境**: 标有黄色图标，需要开发经验，包含IDE配置和项目创建
- **🔴 集成应用**: 标有红色图标，需要专业技术知识，包含高级集成和优化
- **📚 附录资源**: 标有书籍图标，提供补充资源和参考材料

### 📱 使用方式

- **电子阅读**: 支持目录导航和超链接跳转
- **打印版本**: 优化了打印格式，支持A4纸张
- **移动设备**: 响应式设计，支持手机和平板阅读
- **离线使用**: 提供离线安装包和资源下载方式

### 🎯 学习路径建议

**快速上手路径**（适合有经验的开发者）：
1. 第一部分：基础准备 → 1.3 Qt5版本选择指导
2. 第二部分：安装配置 → 2.1 Qt5获取方式 → 2.2 详细安装步骤
3. 第三部分：开发环境 → 3.4 与ShengFan.exe项目集成
4. 第五部分：附录资源 → 5.3 故障排除指南（如遇问题）

**完整学习路径**（适合初学者）：
按照文档顺序从第一部分到第五部分依次学习

## 📋 目录结构

### 第一部分：基础准备 🟢

#### 1. 系统环境准备 🟢
- 1.1 系统要求检查
- 1.2 环境检查工具
- 1.3 Qt5版本选择指导
- 1.4 Dependencies工具准备

### 第二部分：安装配置 🟡

#### 2. Qt5安装与配置 🟡
- 2.1 Qt5获取方式
- 2.2 详细安装步骤
- 2.3 环境变量配置
- 2.4 安装验证

### 第三部分：开发环境 🟡

#### 3. 开发环境搭建 🟡
- 3.1 Qt Creator IDE配置
- 3.2 编译器设置
- 3.3 项目创建和编译测试
- 3.4 与ShengFan.exe项目集成

### 第四部分：集成应用 🔴

#### 4. 高级集成与应用 🔴
- 4.1 与Dependencies工具配合使用
- 4.2 OneNET云平台对接配置
- 4.3 MQTT功能集成
- 4.4 实际案例演示

### 第五部分：附录资源 📚

#### 5. 资源与支持 📚
- 5.1 离线安装包获取方式
- 5.2 视频教程链接
- 5.3 故障排除指南
- 5.4 配置文件模板
- 5.5 常见问题FAQ

## 🔧 技术栈概览

| 技术组件 | 推荐版本 | 用途 | 必需性 |
|---------|----------|------|--------|
| **Qt5框架** | 5.15.2 LTS | 上位机软件开发框架 | 必需 |
| **Qt Creator** | 最新版 | 集成开发环境 | 推荐 |
| **MinGW** | 8.1.0 64-bit | C++编译器 | 必需之一 |
| **MSVC** | 2019/2022 | Microsoft编译器 | 必需之一 |
| **Dependencies工具** | 最新版 | DLL依赖分析 | 推荐 |
| **OneNET云平台** | 最新版 | IoT数据传输 | 可选 |

## 🚀 快速开始

### 最小化安装（仅运行ShengFan.exe）
如果您只需要运行现有的ShengFan.exe程序：
1. 跳转到 [2.1 Qt5获取方式](#21-qt5获取方式) 下载Qt5运行时库
2. 参考 [2.4 安装验证](#24-安装验证) 验证环境
3. 使用 [4.1 Dependencies工具](#41-与dependencies工具配合使用) 诊断问题

### 完整开发环境
如果您需要完整的Qt5开发环境：
1. 按顺序阅读第一部分到第三部分
2. 根据需要参考第四部分的集成应用
3. 遇到问题时查阅第五部分的资源支持

## ⚠️ 重要提醒

- **版本兼容性**: 本文档专注于Qt5.15.2 LTS版本，与ShengFan.exe完全兼容
- **系统要求**: 强烈建议使用Windows 10/11 64位系统
- **网络需求**: 在线安装需要稳定的网络连接，建议准备离线安装包
- **磁盘空间**: 完整安装需要约15-20GB磁盘空间
- **权限要求**: 部分操作需要管理员权限

## 📞 技术支持

| 支持类型 | 联系方式 | 服务时间 | 响应时间 |
|---------|----------|----------|----------|
| **紧急支持** | 400-123-4567 | 24小时 | 30分钟内 |
| **技术咨询** | 400-123-4568 | 工作日 9:00-18:00 | 2小时内 |
| **邮件支持** | <EMAIL> | 24小时 | 4小时内 |
| **在线文档** | https://docs.smartmeter.com/qt5 | 24小时 | 即时访问 |

---

**注意**: 本文档与《智能电表上位机操作文档》配套使用，建议同时参考以获得完整的技术支持。

---

## 版权信息

- **版权所有**: © 2025 智能电表项目团队
- **使用许可**: 仅供内部使用，禁止外部传播
- **免责声明**: 本文档仅供参考，实际操作请以软件实际功能为准
- **商标声明**: 文档中提及的商标归各自所有者所有

---

# 第一部分：基础准备 🟢

## 1. 系统环境准备 🟢

### 1.1 系统要求检查

#### 🖥️ 操作系统要求

**支持的Windows版本**：
```
✅ Windows 11 (推荐)
   - 版本 21H2 或更高
   - 所有版本（家庭版、专业版、企业版）

✅ Windows 10 (兼容)
   - 版本 1903 (19H1) 或更高
   - 64位版本强烈推荐
   - 32位版本仅限特殊需求

❌ 不支持的系统
   - Windows 8.1 及更早版本
   - Windows Server 2016 及更早版本
```

**系统版本检查方法**：
```cmd
# 方法1：命令行检查
winver

# 方法2：系统信息
msinfo32

# 方法3：PowerShell检查
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion
```

#### 💾 硬件要求

**最低配置**：
| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **处理器** | Intel i3 / AMD 同等级 | Intel i5 / AMD Ryzen 5 | 64位处理器 |
| **内存** | 4GB RAM | 8GB RAM | 开发环境推荐16GB |
| **磁盘空间** | 15GB 可用空间 | 25GB 可用空间 | SSD推荐 |
| **显卡** | 集成显卡 | 独立显卡 | 支持OpenGL 2.0+ |
| **网络** | 宽带连接 | 稳定宽带 | 用于下载和更新 |

**硬件检查脚本**：
```powershell
# 系统硬件信息检查
Write-Host "=== Qt5环境硬件检查 ===" -ForegroundColor Green

# 检查处理器
$cpu = Get-WmiObject -Class Win32_Processor
Write-Host "处理器: $($cpu.Name)" -ForegroundColor Yellow
Write-Host "核心数: $($cpu.NumberOfCores)" -ForegroundColor Yellow

# 检查内存
$memory = Get-WmiObject -Class Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
Write-Host "总内存: ${totalMemoryGB}GB" -ForegroundColor Yellow

if ($totalMemoryGB -ge 8) {
    Write-Host "✅ 内存充足，适合Qt5开发" -ForegroundColor Green
} elseif ($totalMemoryGB -ge 4) {
    Write-Host "⚠️ 内存基本满足，建议升级到8GB" -ForegroundColor Yellow
} else {
    Write-Host "❌ 内存不足，强烈建议升级" -ForegroundColor Red
}

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}
foreach ($drive in $disk) {
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    Write-Host "驱动器 $($drive.DeviceID) 可用空间: ${freeSpaceGB}GB" -ForegroundColor Yellow
}
```

#### 🌐 网络连接要求

**网络需求**：
- **下载速度**: 最低10Mbps，推荐50Mbps+
- **稳定性**: 支持大文件下载（Qt5安装包约3-5GB）
- **访问权限**: 能够访问qt.io、github.com等国外网站
- **代理设置**: 如需代理，确保支持HTTPS协议

**网络连接测试**：
```cmd
# 测试网络连通性
ping qt.io
ping download.qt.io
ping github.com

# 测试下载速度（PowerShell）
Measure-Command {
    Invoke-WebRequest -Uri "https://download.qt.io/online/qtsdkrepository/windows_x86/desktop/qt5_5152/qt.qt5.5152.win64_mingw81/5.15.2-0-202011130602qtbase-Windows-Windows_10-Mingw-Windows-Windows_10-X86_64.7z" -Method Head
}
```

### 1.2 环境检查工具

#### 🔍 系统信息查看方法

**方法1：Windows系统信息工具**
```cmd
# 启动系统信息
msinfo32

# 关键检查项目：
# - 系统摘要 → 操作系统名称和版本
# - 硬件资源 → 内存和处理器信息
# - 组件 → 显示设备信息
```

**方法2：PowerShell系统检查**
```powershell
# 完整系统信息检查脚本
function Check-Qt5Environment {
    Write-Host "=== Qt5环境兼容性检查 ===" -ForegroundColor Cyan

    # 操作系统检查
    $os = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory
    Write-Host "`n操作系统信息:" -ForegroundColor Green
    Write-Host "  系统: $($os.WindowsProductName)" -ForegroundColor White
    Write-Host "  版本: $($os.WindowsVersion)" -ForegroundColor White

    # 内存检查
    $memoryGB = [math]::Round($os.TotalPhysicalMemory / 1GB, 2)
    Write-Host "  内存: ${memoryGB}GB" -ForegroundColor White

    # 磁盘空间检查
    Write-Host "`n磁盘空间:" -ForegroundColor Green
    Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
        $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        $totalGB = [math]::Round($_.Size / 1GB, 2)
        Write-Host "  驱动器 $($_.DeviceID) ${freeGB}GB / ${totalGB}GB 可用" -ForegroundColor White
    }

    # .NET Framework检查
    Write-Host "`n.NET Framework:" -ForegroundColor Green
    $dotnet = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
              Get-ItemProperty -Name Version -EA 0 |
              Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
              Select-Object PSChildName, Version
    $dotnet | ForEach-Object {
        Write-Host "  $($_.PSChildName): $($_.Version)" -ForegroundColor White
    }

    Write-Host "`n检查完成！" -ForegroundColor Cyan
}

# 执行检查
Check-Qt5Environment
```

#### 📋 已安装软件检查

**检查已安装的开发工具**：
```powershell
# 检查已安装的相关软件
function Check-InstalledDevelopmentTools {
    Write-Host "=== 开发工具安装检查 ===" -ForegroundColor Cyan

    # 检查Visual Studio
    $vsInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Visual Studio*"}
    if ($vsInstallations) {
        Write-Host "`nVisual Studio:" -ForegroundColor Green
        $vsInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查Qt相关软件
    $qtInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Qt*"}
    if ($qtInstallations) {
        Write-Host "`nQt相关软件:" -ForegroundColor Green
        $qtInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查MinGW
    if (Test-Path "C:\Qt\Tools\mingw*") {
        Write-Host "`nMinGW编译器:" -ForegroundColor Green
        Get-ChildItem "C:\Qt\Tools\mingw*" | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor White
        }
    }

    # 检查Git
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Host "`nGit: $gitVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "`nGit: 未安装" -ForegroundColor Yellow
    }
}

Check-InstalledDevelopmentTools
```

#### 🔧 注册表检查脚本

**Visual C++ Redistributable检查**：
```powershell
# 检查VC++ Redistributable安装情况
function Check-VCRedistributable {
    Write-Host "=== Visual C++ Redistributable 检查 ===" -ForegroundColor Cyan

    $vcRedist = @()

    # 检查64位版本
    $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                 Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                 Select-Object DisplayName, DisplayVersion, InstallDate

    # 检查32位版本（在64位系统上）
    if ([Environment]::Is64BitOperatingSystem) {
        $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                     Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                     Select-Object DisplayName, DisplayVersion, InstallDate
    }

    if ($vcRedist) {
        Write-Host "`n已安装的VC++ Redistributable:" -ForegroundColor Green
        $vcRedist | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  $($_.DisplayName) - $($_.DisplayVersion)" -ForegroundColor White
        }
    } else {
        Write-Host "`n⚠️ 未检测到VC++ Redistributable，可能需要安装" -ForegroundColor Yellow
    }
}

Check-VCRedistributable
```

---

# 第一部分：基础准备 🟢

## 1. 系统环境准备 🟢

### 1.1 系统要求检查

#### 🖥️ 操作系统要求

**支持的Windows版本**：
```
✅ Windows 11 (推荐)
   - 版本 21H2 或更高
   - 所有版本（家庭版、专业版、企业版）

✅ Windows 10 (兼容)
   - 版本 1903 (19H1) 或更高
   - 64位版本强烈推荐
   - 32位版本仅限特殊需求

❌ 不支持的系统
   - Windows 8.1 及更早版本
   - Windows Server 2016 及更早版本
```

**系统版本检查方法**：
```cmd
# 方法1：命令行检查
winver

# 方法2：系统信息
msinfo32

# 方法3：PowerShell检查
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion
```

#### 💾 硬件要求

**最低配置**：
| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **处理器** | Intel i3 / AMD 同等级 | Intel i5 / AMD Ryzen 5 | 64位处理器 |
| **内存** | 4GB RAM | 8GB RAM | 开发环境推荐16GB |
| **磁盘空间** | 15GB 可用空间 | 25GB 可用空间 | SSD推荐 |
| **显卡** | 集成显卡 | 独立显卡 | 支持OpenGL 2.0+ |
| **网络** | 宽带连接 | 稳定宽带 | 用于下载和更新 |

**硬件检查脚本**：
```powershell
# 系统硬件信息检查
Write-Host "=== Qt5环境硬件检查 ===" -ForegroundColor Green

# 检查处理器
$cpu = Get-WmiObject -Class Win32_Processor
Write-Host "处理器: $($cpu.Name)" -ForegroundColor Yellow
Write-Host "核心数: $($cpu.NumberOfCores)" -ForegroundColor Yellow

# 检查内存
$memory = Get-WmiObject -Class Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
Write-Host "总内存: ${totalMemoryGB}GB" -ForegroundColor Yellow

if ($totalMemoryGB -ge 8) {
    Write-Host "✅ 内存充足，适合Qt5开发" -ForegroundColor Green
} elseif ($totalMemoryGB -ge 4) {
    Write-Host "⚠️ 内存基本满足，建议升级到8GB" -ForegroundColor Yellow
} else {
    Write-Host "❌ 内存不足，强烈建议升级" -ForegroundColor Red
}

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}
foreach ($drive in $disk) {
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    Write-Host "驱动器 $($drive.DeviceID) 可用空间: ${freeSpaceGB}GB" -ForegroundColor Yellow
}
```

#### 🌐 网络连接要求

**网络需求**：
- **下载速度**: 最低10Mbps，推荐50Mbps+
- **稳定性**: 支持大文件下载（Qt5安装包约3-5GB）
- **访问权限**: 能够访问qt.io、github.com等国外网站
- **代理设置**: 如需代理，确保支持HTTPS协议

**网络连接测试**：
```cmd
# 测试网络连通性
ping qt.io
ping download.qt.io
ping github.com

# 测试下载速度（PowerShell）
Measure-Command {
    Invoke-WebRequest -Uri "https://download.qt.io/online/qtsdkrepository/windows_x86/desktop/qt5_5152/qt.qt5.5152.win64_mingw81/5.15.2-0-202011130602qtbase-Windows-Windows_10-Mingw-Windows-Windows_10-X86_64.7z" -Method Head
}
```

### 1.2 环境检查工具

#### 🔍 系统信息查看方法

**方法1：Windows系统信息工具**
```cmd
# 启动系统信息
msinfo32

# 关键检查项目：
# - 系统摘要 → 操作系统名称和版本
# - 硬件资源 → 内存和处理器信息
# - 组件 → 显示设备信息
```

**方法2：PowerShell系统检查**
```powershell
# 完整系统信息检查脚本
function Check-Qt5Environment {
    Write-Host "=== Qt5环境兼容性检查 ===" -ForegroundColor Cyan

    # 操作系统检查
    $os = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory
    Write-Host "`n操作系统信息:" -ForegroundColor Green
    Write-Host "  系统: $($os.WindowsProductName)" -ForegroundColor White
    Write-Host "  版本: $($os.WindowsVersion)" -ForegroundColor White

    # 内存检查
    $memoryGB = [math]::Round($os.TotalPhysicalMemory / 1GB, 2)
    Write-Host "  内存: ${memoryGB}GB" -ForegroundColor White

    # 磁盘空间检查
    Write-Host "`n磁盘空间:" -ForegroundColor Green
    Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
        $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        $totalGB = [math]::Round($_.Size / 1GB, 2)
        Write-Host "  驱动器 $($_.DeviceID) ${freeGB}GB / ${totalGB}GB 可用" -ForegroundColor White
    }

    # .NET Framework检查
    Write-Host "`n.NET Framework:" -ForegroundColor Green
    $dotnet = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
              Get-ItemProperty -Name Version -EA 0 |
              Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
              Select-Object PSChildName, Version
    $dotnet | ForEach-Object {
        Write-Host "  $($_.PSChildName): $($_.Version)" -ForegroundColor White
    }

    Write-Host "`n检查完成！" -ForegroundColor Cyan
}

# 执行检查
Check-Qt5Environment
```

#### 📋 已安装软件检查

**检查已安装的开发工具**：
```powershell
# 检查已安装的相关软件
function Check-InstalledDevelopmentTools {
    Write-Host "=== 开发工具安装检查 ===" -ForegroundColor Cyan

    # 检查Visual Studio
    $vsInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Visual Studio*"}
    if ($vsInstallations) {
        Write-Host "`nVisual Studio:" -ForegroundColor Green
        $vsInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查Qt相关软件
    $qtInstallations = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Qt*"}
    if ($qtInstallations) {
        Write-Host "`nQt相关软件:" -ForegroundColor Green
        $qtInstallations | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Version)" -ForegroundColor White
        }
    }

    # 检查MinGW
    if (Test-Path "C:\Qt\Tools\mingw*") {
        Write-Host "`nMinGW编译器:" -ForegroundColor Green
        Get-ChildItem "C:\Qt\Tools\mingw*" | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor White
        }
    }

    # 检查Git
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Host "`nGit: $gitVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "`nGit: 未安装" -ForegroundColor Yellow
    }
}

Check-InstalledDevelopmentTools
```

#### 🔧 注册表检查脚本

**Visual C++ Redistributable检查**：
```powershell
# 检查VC++ Redistributable安装情况
function Check-VCRedistributable {
    Write-Host "=== Visual C++ Redistributable 检查 ===" -ForegroundColor Cyan

    $vcRedist = @()

    # 检查64位版本
    $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                 Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                 Select-Object DisplayName, DisplayVersion, InstallDate

    # 检查32位版本（在64位系统上）
    if ([Environment]::Is64BitOperatingSystem) {
        $vcRedist += Get-ItemProperty "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" |
                     Where-Object {$_.DisplayName -like "*Visual C++*Redistributable*"} |
                     Select-Object DisplayName, DisplayVersion, InstallDate
    }

    if ($vcRedist) {
        Write-Host "`n已安装的VC++ Redistributable:" -ForegroundColor Green
        $vcRedist | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  $($_.DisplayName) - $($_.DisplayVersion)" -ForegroundColor White
        }
    } else {
        Write-Host "`n⚠️ 未检测到VC++ Redistributable，可能需要安装" -ForegroundColor Yellow
    }
}

Check-VCRedistributable
```

### 1.3 Qt5版本选择指导

#### 🎯 Qt5.15.2 LTS推荐理由

**为什么选择Qt5.15.2 LTS**：
```
✅ 长期支持版本 (LTS)
   - 官方支持至2025年12月
   - 稳定性和兼容性最佳
   - 商业和开源双重支持

✅ 与ShengFan.exe完全兼容
   - 经过充分测试验证
   - 所有依赖库版本匹配
   - MQTT功能完整支持

✅ Windows 10/11最佳适配
   - 原生支持最新Windows API
   - 高DPI显示完美支持
   - 现代化界面风格

✅ 开发工具链成熟
   - Qt Creator完美集成
   - MinGW 8.1.0稳定支持
   - MSVC 2019/2022兼容
```

#### 📊 版本兼容性分析

**Qt5版本对比表**：
| 版本 | 发布时间 | LTS状态 | Windows支持 | ShengFan.exe兼容性 | 推荐度 |
|------|----------|---------|--------------|-------------------|--------|
| **Qt 5.15.2** | 2020.11 | ✅ LTS | Win10/11 | ✅ 完全兼容 | ⭐⭐⭐⭐⭐ |
| Qt 5.15.1 | 2020.09 | ❌ 非LTS | Win10/11 | ✅ 兼容 | ⭐⭐⭐⭐ |
| Qt 5.14.2 | 2020.03 | ❌ 停止支持 | Win10 | ⚠️ 部分兼容 | ⭐⭐⭐ |
| Qt 5.12.12 | 2021.11 | ✅ LTS已结束 | Win10 | ⚠️ 需要调整 | ⭐⭐ |
| Qt 6.x | 2020+ | ✅ 最新 | Win10/11 | ❌ 不兼容 | ⭐ |

**关键兼容性要素**：
```
🔍 MQTT模块支持
✅ Qt5.15.2: Qt5Mqtt.dll 完整支持
✅ Qt5.15.1: Qt5Mqtt.dll 基本支持
⚠️ Qt5.14.x: MQTT功能有限
❌ Qt6.x: MQTT模块重构，不兼容

🔍 编译器支持
✅ Qt5.15.2: MinGW 8.1.0 + MSVC 2019/2022
✅ Qt5.15.1: MinGW 8.1.0 + MSVC 2019
⚠️ Qt5.14.x: MinGW 7.3.0 + MSVC 2017
❌ Qt6.x: 需要C++17支持

🔍 依赖库版本
✅ Qt5.15.2: 与ShengFan.exe依赖完全匹配
⚠️ 其他版本: 可能存在版本冲突
```

#### 🎯 与ShengFan.exe的匹配性

**依赖库版本匹配验证**：
```powershell
# ShengFan.exe依赖库版本检查脚本
function Check-ShengFanDependencies {
    $shengfanPath = ".\ShengFan.exe"

    if (-not (Test-Path $shengfanPath)) {
        Write-Host "❌ 未找到ShengFan.exe" -ForegroundColor Red
        return
    }

    Write-Host "=== ShengFan.exe Qt5依赖检查 ===" -ForegroundColor Cyan

    # 检查Qt5核心库
    $qtLibs = @(
        "Qt5Core.dll",
        "Qt5Gui.dll",
        "Qt5Widgets.dll",
        "Qt5Network.dll",
        "Qt5Mqtt.dll",
        "Qt5PrintSupport.dll",
        "Qt5Svg.dll"
    )

    foreach ($lib in $qtLibs) {
        if (Test-Path $lib) {
            $version = (Get-ItemProperty $lib).VersionInfo.FileVersion
            Write-Host "✅ $lib - 版本: $version" -ForegroundColor Green
        } else {
            Write-Host "❌ $lib - 缺失" -ForegroundColor Red
        }
    }

    # 检查运行时库
    $runtimeLibs = @(
        "libgcc_s_dw2-1.dll",
        "libstdc++-6.dll",
        "libwinpthread-1.dll"
    )

    Write-Host "`n运行时库检查:" -ForegroundColor Yellow
    foreach ($lib in $runtimeLibs) {
        if (Test-Path $lib) {
            Write-Host "✅ $lib" -ForegroundColor Green
        } else {
            Write-Host "❌ $lib - 缺失" -ForegroundColor Red
        }
    }
}

Check-ShengFanDependencies
```

**推荐安装配置**：
```
📦 Qt5.15.2 LTS 推荐组件
├─ Qt5.15.2
│  ├─ MinGW 8.1.0 64-bit ✅ 必需
│  ├─ MSVC 2019 64-bit ✅ 推荐
│  ├─ Qt5Mqtt ✅ 必需（MQTT功能）
│  ├─ Qt Charts ⚠️ 可选（图表功能）
│  └─ Qt Virtual Keyboard ❌ 不需要
├─ Qt Creator 4.15.x ✅ 推荐
├─ MinGW 8.1.0 ✅ 必需
├─ CMake 3.19+ ⚠️ 可选
└─ Ninja ⚠️ 可选
```

### 1.4 Dependencies工具准备

#### 🔧 Dependencies工具介绍

**Dependencies vs Dependency Walker**：
```
🆚 对比分析
┌─────────────────┬─────────────────┬─────────────────┐
│ 特性            │ Dependencies    │ Dependency Walker│
├─────────────────┼─────────────────┼─────────────────┤
│ Windows 11支持  │ ✅ 完美支持     │ ❌ 兼容性问题   │
│ 64位程序分析    │ ✅ 原生支持     │ ⚠️ 有限支持     │
│ 现代化界面      │ ✅ 现代化UI     │ ❌ 老旧界面     │
│ API Sets支持    │ ✅ 完整支持     │ ❌ 不支持       │
│ 更新维护        │ ✅ 活跃开发     │ ❌ 停止更新     │
│ 开源免费        │ ✅ MIT许可      │ ✅ 免费         │
└─────────────────┴─────────────────┴─────────────────┘
```

**Dependencies主要功能**：
- **DLL依赖分析**: 递归分析所有依赖关系
- **缺失库检测**: 自动识别缺失的DLL文件
- **版本冲突诊断**: 检测版本不匹配问题
- **API Sets支持**: 支持Windows 10/11的API Sets
- **导入/导出函数**: 详细的函数级别分析
- **现代化界面**: 支持高DPI和暗色主题

#### 📥 下载和基本配置

**官方下载地址**：
```
🌐 主要下载源
├─ GitHub官方: https://github.com/lucasg/Dependencies
├─ 发布页面: https://github.com/lucasg/Dependencies/releases
└─ 推荐版本: v1.11.1 或更新版本

📦 下载文件
├─ Dependencies_x64_Release.zip (64位推荐)
├─ Dependencies_x86_Release.zip (32位兼容)
└─ 文件大小: 约2-3MB
```

**安装配置步骤**：
```cmd
# 1. 创建工具目录
mkdir C:\Tools\Dependencies
cd C:\Tools\Dependencies

# 2. 解压下载的文件
# 将Dependencies_x64_Release.zip解压到当前目录

# 3. 验证安装
Dependencies.exe --version

# 4. 添加到系统PATH（可选）
setx PATH "%PATH%;C:\Tools\Dependencies"
```

**右键菜单集成**：
```batch
@echo off
REM 创建右键菜单集成脚本
echo 正在配置Dependencies右键菜单...

REM 创建注册表文件
echo Windows Registry Editor Version 5.00 > add_dependencies_menu.reg
echo. >> add_dependencies_menu.reg
echo [HKEY_CLASSES_ROOT\exefile\shell\Dependencies] >> add_dependencies_menu.reg
echo @="使用Dependencies分析" >> add_dependencies_menu.reg
echo. >> add_dependencies_menu.reg
echo [HKEY_CLASSES_ROOT\exefile\shell\Dependencies\command] >> add_dependencies_menu.reg
echo @="\"C:\\Tools\\Dependencies\\Dependencies.exe\" \"%%1\"" >> add_dependencies_menu.reg

REM 导入注册表
regedit /s add_dependencies_menu.reg

echo Dependencies右键菜单配置完成！
pause
```

#### 🔗 与Qt5分析的关联性

**Dependencies在Qt5开发中的作用**：
```
🎯 主要应用场景
├─ Qt5安装验证
│  ├─ 检查Qt5库文件完整性
│  ├─ 验证版本一致性
│  └─ 识别缺失组件
├─ ShengFan.exe依赖分析
│  ├─ 分析Qt5依赖关系
│  ├─ 检测DLL版本冲突
│  └─ 诊断运行时错误
├─ 部署环境检查
│  ├─ 确认运行时库完整
│  ├─ 验证目标机器兼容性
│  └─ 优化部署包大小
└─ 故障排除诊断
   ├─ 定位DLL加载失败
   ├─ 分析API调用问题
   └─ 解决版本冲突
```

**Qt5核心库分析要点**：
```
🔍 重点关注的Qt5依赖库
✅ Qt5Core.dll      - Qt基础核心功能
✅ Qt5Gui.dll       - 图形用户界面
✅ Qt5Widgets.dll   - 窗口控件系统
✅ Qt5Network.dll   - 网络通信功能
⭐ Qt5Mqtt.dll      - MQTT协议支持（关键）
✅ Qt5PrintSupport.dll - 打印功能支持
✅ Qt5Svg.dll       - SVG矢量图形

🔍 运行时库检查要点
✅ libgcc_s_dw2-1.dll    - GCC运行时库
✅ libstdc++-6.dll      - C++标准库
✅ libwinpthread-1.dll  - 线程支持库

🔍 状态指示器含义
🟢 绿色图标：DLL正常加载，无问题
🟡 黄色图标：存在警告，但不影响运行
🔴 红色图标：DLL缺失或严重错误
🔵 蓝色图标：系统DLL或API Sets
```

**Dependencies快速分析脚本**：
```batch
@echo off
REM Qt5项目Dependencies批量分析脚本
echo ================================
echo Qt5项目Dependencies分析工具
echo ================================

set DEPS_PATH=C:\Tools\Dependencies\Dependencies.exe
set TARGET_DIR=%~dp0

if not exist "%DEPS_PATH%" (
    echo ❌ 错误：未找到Dependencies工具
    echo 请确保Dependencies已安装到 C:\Tools\Dependencies\
    pause
    exit /b 1
)

echo 📂 分析目录: %TARGET_DIR%
echo.

REM 分析ShengFan.exe
if exist "%TARGET_DIR%ShengFan.exe" (
    echo 🔍 正在分析 ShengFan.exe...
    "%DEPS_PATH%" "%TARGET_DIR%ShengFan.exe"
    echo ✅ ShengFan.exe 分析完成
) else (
    echo ⚠️ 未找到 ShengFan.exe
)

echo.
echo 📋 分析完成！请查看Dependencies窗口中的结果
echo.
echo 🔍 重点检查项目：
echo   - Qt5*.dll 是否全部为绿色状态
echo   - 是否存在红色（缺失）的依赖库
echo   - 版本号是否一致
echo.
pause
```

**常见Qt5依赖问题诊断**：
```
❌ 常见问题及解决方案

问题1：Qt5Core.dll显示红色
原因：Qt5未正确安装或PATH环境变量未设置
解决：重新安装Qt5或配置环境变量

问题2：版本号不一致
原因：混合了不同版本的Qt5库
解决：清理旧版本，重新安装Qt5.15.2

问题3：libgcc_s_dw2-1.dll缺失
原因：MinGW运行时库未部署
解决：复制MinGW运行时库到程序目录

问题4：Qt5Mqtt.dll显示黄色警告
原因：MQTT模块版本不匹配
解决：确保使用Qt5.15.2完整安装包

问题5：API Sets相关错误
原因：Windows版本过低或系统文件损坏
解决：更新Windows或运行sfc /scannow
```

**Dependencies使用最佳实践**：
```
📋 分析流程建议
1️⃣ 环境准备
   - 确保Dependencies已正确安装
   - 配置右键菜单快捷方式
   - 准备分析目标文件

2️⃣ 基础分析
   - 打开ShengFan.exe进行分析
   - 检查依赖关系树的完整性
   - 记录所有红色和黄色警告

3️⃣ 深入诊断
   - 逐个检查Qt5核心库状态
   - 验证版本号一致性
   - 分析运行时库依赖

4️⃣ 问题解决
   - 根据分析结果定位问题
   - 参考故障排除指南
   - 验证修复效果

5️⃣ 文档记录
   - 记录分析结果和解决方案
   - 建立问题知识库
   - 为团队提供参考
```

---

## 📝 第一部分总结

### ✅ 完成的准备工作

通过第一部分的学习，您应该已经完成了以下准备工作：

1. **系统环境检查** 🟢
   - ✅ 确认Windows 10/11系统兼容性
   - ✅ 验证硬件配置满足要求
   - ✅ 测试网络连接稳定性

2. **环境检查工具** 🟢
   - ✅ 掌握系统信息查看方法
   - ✅ 学会已安装软件检查
   - ✅ 了解注册表检查脚本

3. **Qt5版本选择** 🟢
   - ✅ 理解Qt5.15.2 LTS的优势
   - ✅ 掌握版本兼容性分析方法
   - ✅ 确认与ShengFan.exe的匹配性

4. **Dependencies工具** 🟢
   - ✅ 完成Dependencies工具下载安装
   - ✅ 配置右键菜单快捷方式
   - ✅ 理解与Qt5分析的关联性

### 🎯 下一步行动

现在您已经完成了基础准备工作，可以继续进行：
- **第二部分：安装配置** 🟡 - Qt5的获取、安装和环境配置
- **第三部分：开发环境** 🟡 - Qt Creator IDE和编译器设置
- **第四部分：集成应用** 🔴 - 高级集成和实际案例演示

---

# 第二部分：安装配置 🟡

## 2. Qt5安装与配置 🟡

### 2.1 Qt5获取方式

#### 🌐 官方在线安装器使用方法

**Qt Online Installer优势**：
```
✅ 在线安装器优点
├─ 自动获取最新版本
├─ 智能组件选择
├─ 增量更新支持
├─ 官方数字签名验证
└─ 自动依赖解析

⚠️ 注意事项
├─ 需要稳定网络连接
├─ 下载速度依赖网络
├─ 需要Qt账户登录
└─ 某些地区访问较慢
```

**下载官方在线安装器**：
```
🔗 官方下载地址
├─ 主站: https://www.qt.io/download-qt-installer
├─ 直链: https://download.qt.io/official_releases/online_installers/
├─ 文件名: qt-unified-windows-x64-4.6.1-online.exe
└─ 文件大小: 约30MB

📋 系统要求
├─ Windows 10/11 64位
├─ 管理员权限
├─ 稳定网络连接
└─ 至少15GB可用空间
```

**在线安装器使用步骤**：
```
📝 详细安装流程
1️⃣ 下载并启动安装器
   - 右键"以管理员身份运行"
   - 等待初始化完成

2️⃣ 登录Qt账户
   - 使用现有账户或注册新账户
   - 选择"Open Source"免费版本

3️⃣ 选择安装路径
   - 推荐路径: C:\Qt
   - 确保有足够磁盘空间

4️⃣ 组件选择（重要）
   - Qt 5.15.2 LTS
   - MinGW 8.1.0 64-bit
   - Qt Creator 4.15.x
   - Qt5Mqtt模块

5️⃣ 开始安装
   - 确认选择并开始下载
   - 等待安装完成（约30-60分钟）
```

#### 📦 离线安装包下载和使用

**离线安装包优势**：
```
✅ 离线安装优点
├─ 无需网络连接
├─ 安装速度快
├─ 可重复使用
├─ 版本固定稳定
└─ 适合批量部署

⚠️ 注意事项
├─ 文件体积较大（3-5GB）
├─ 需要手动更新
├─ 组件选择有限
└─ 下载时间较长
```

**官方离线包下载地址**：
```
🔗 Qt5.15.2 LTS离线包
├─ 官方FTP: https://download.qt.io/archive/qt/5.15/5.15.2/
├─ 推荐文件: qt-opensource-windows-x86-5.15.2.exe
├─ 文件大小: 约4.2GB
└─ MD5校验: 建议下载后验证

📋 组件说明
├─ qt-opensource-windows-x86-5.15.2.exe (完整版)
├─ 包含Qt Creator IDE
├─ 包含MinGW 8.1.0编译器
├─ 包含所有标准模块
└─ 包含Qt5Mqtt模块
```

**离线安装包使用方法**：
```
📝 离线安装流程
1️⃣ 下载验证
   # PowerShell验证MD5
   Get-FileHash qt-opensource-windows-x86-5.15.2.exe -Algorithm MD5

2️⃣ 启动安装
   - 右键"以管理员身份运行"
   - 跳过在线账户登录

3️⃣ 许可协议
   - 选择"Open Source"
   - 接受GPL v3许可协议

4️⃣ 安装路径设置
   - 默认: C:\Qt\Qt5.15.2
   - 自定义: 确保路径无中文和空格

5️⃣ 组件选择
   - 保持默认选择
   - 确保包含MinGW 8.1.0
   - 确保包含Qt Creator

6️⃣ 完成安装
   - 等待文件复制完成
   - 配置开始菜单快捷方式
```

#### 🇨🇳 国内镜像站点推荐

**清华大学镜像站**：
```
🔗 清华大学开源软件镜像站
├─ 主页: https://mirrors.tuna.tsinghua.edu.cn/qt/
├─ Qt5.15.2: https://mirrors.tuna.tsinghua.edu.cn/qt/archive/qt/5.15/5.15.2/
├─ 在线安装器: https://mirrors.tuna.tsinghua.edu.cn/qt/official_releases/online_installers/
└─ 更新频率: 每日同步

📊 速度测试
├─ 国内访问速度: ⭐⭐⭐⭐⭐
├─ 稳定性: ⭐⭐⭐⭐⭐
├─ 完整性: ⭐⭐⭐⭐⭐
└─ 推荐指数: ⭐⭐⭐⭐⭐
```

**中科大镜像站**：
```
🔗 中科大开源软件镜像站
├─ 主页: https://mirrors.ustc.edu.cn/qtproject/
├─ Qt5.15.2: https://mirrors.ustc.edu.cn/qtproject/archive/qt/5.15/5.15.2/
├─ 在线安装器: https://mirrors.ustc.edu.cn/qtproject/official_releases/online_installers/
└─ 更新频率: 每日同步

📊 速度测试
├─ 国内访问速度: ⭐⭐⭐⭐⭐
├─ 稳定性: ⭐⭐⭐⭐
├─ 完整性: ⭐⭐⭐⭐⭐
└─ 推荐指数: ⭐⭐⭐⭐
```

**配置镜像源方法**：
```cmd
REM 配置Qt在线安装器使用国内镜像
REM 方法1：命令行参数
qt-unified-windows-x64-online.exe --mirror https://mirrors.tuna.tsinghua.edu.cn/qt

REM 方法2：环境变量设置
set QT_MIRROR_URL=https://mirrors.tuna.tsinghua.edu.cn/qt
qt-unified-windows-x64-online.exe

REM 方法3：配置文件修改（高级用户）
REM 编辑 %APPDATA%\QtProject\qtcreator\QtProject.conf
REM 添加: MirrorUrl=https://mirrors.tuna.tsinghua.edu.cn/qt
```

#### 📊 各种方式的优缺点对比

**安装方式对比表**：
| 安装方式 | 网络要求 | 安装速度 | 文件大小 | 更新便利性 | 推荐场景 | 推荐度 |
|---------|----------|----------|----------|------------|----------|--------|
| **在线安装器** | 高速稳定网络 | 中等 | 30MB | ⭐⭐⭐⭐⭐ | 个人开发 | ⭐⭐⭐⭐ |
| **离线安装包** | 仅下载时需要 | 快速 | 4.2GB | ⭐⭐ | 批量部署 | ⭐⭐⭐⭐⭐ |
| **清华镜像** | 中速网络 | 快速 | 30MB/4.2GB | ⭐⭐⭐⭐ | 国内用户 | ⭐⭐⭐⭐⭐ |
| **中科大镜像** | 中速网络 | 快速 | 30MB/4.2GB | ⭐⭐⭐⭐ | 国内用户 | ⭐⭐⭐⭐ |

**选择建议**：
```
🎯 推荐选择策略

情况1：首次安装 + 国内用户
推荐：清华镜像 + 离线安装包
理由：下载速度快，安装稳定

情况2：已有Qt经验 + 需要定制
推荐：官方在线安装器
理由：组件选择灵活，更新方便

情况3：企业批量部署
推荐：离线安装包
理由：统一版本，无网络依赖

情况4：网络环境不稳定
推荐：离线安装包
理由：一次下载，多次使用

情况5：学习测试用途
推荐：清华镜像 + 在线安装器
理由：快速获取，便于更新
```

### 2.2 详细安装步骤

#### 🔧 在线安装器完整流程

**步骤1：准备工作**
```powershell
# 安装前环境检查脚本
function Pre-InstallCheck {
    Write-Host "=== Qt5安装前检查 ===" -ForegroundColor Cyan

    # 检查管理员权限
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-Host "❌ 需要管理员权限运行" -ForegroundColor Red
        return $false
    }

    # 检查磁盘空间
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}).FreeSpace / 1GB
    if ($freeSpace -lt 20) {
        Write-Host "❌ C盘空间不足，需要至少20GB" -ForegroundColor Red
        return $false
    }

    # 检查网络连接
    try {
        Test-NetConnection -ComputerName "download.qt.io" -Port 443 -InformationLevel Quiet
        Write-Host "✅ 网络连接正常" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 网络连接异常，建议使用离线安装包" -ForegroundColor Yellow
    }

    Write-Host "✅ 安装前检查完成" -ForegroundColor Green
    return $true
}

Pre-InstallCheck
```

**步骤2：下载并启动安装器**
```cmd
REM 下载在线安装器
curl -L -o qt-unified-windows-x64-online.exe https://download.qt.io/official_releases/online_installers/qt-unified-windows-x64-online.exe

REM 验证文件完整性
certutil -hashfile qt-unified-windows-x64-online.exe SHA256

REM 以管理员身份启动
powershell -Command "Start-Process qt-unified-windows-x64-online.exe -Verb RunAs"
```

**步骤3：安装器界面操作**
```
📱 界面操作指南

🔹 欢迎界面
   - 点击"Next"继续
   - 阅读并接受许可协议

🔹 Qt账户登录
   - 输入Qt账户邮箱和密码
   - 或点击"Skip"跳过（功能受限）
   - 选择"Open Source"使用免费版

🔹 安装路径选择
   - 默认路径：C:\Qt
   - 自定义路径：避免中文和空格
   - 确保目标磁盘有足够空间

🔹 组件选择（关键步骤）
   ✅ Qt 5.15.2 LTS
      ├─ MinGW 8.1.0 64-bit ✅ 必选
      ├─ MSVC 2019 64-bit ⚠️ 可选
      ├─ Qt5Mqtt ✅ 必选
      ├─ Qt Charts ⚠️ 可选
      └─ Sources ❌ 不需要
   ✅ Qt Creator 4.15.x ✅ 推荐
   ✅ MinGW 8.1.0 ✅ 必选
   ❌ Qt Design Studio ❌ 不需要

🔹 开始安装
   - 确认组件选择
   - 点击"Install"开始下载
   - 等待安装完成（30-60分钟）
```

**步骤4：安装过程监控**
```powershell
# 安装过程监控脚本
function Monitor-QtInstallation {
    $qtPath = "C:\Qt"
    $startTime = Get-Date

    Write-Host "=== Qt5安装进度监控 ===" -ForegroundColor Cyan
    Write-Host "开始时间: $startTime" -ForegroundColor Yellow

    while ($true) {
        if (Test-Path "$qtPath\5.15.2") {
            $currentSize = (Get-ChildItem -Path "$qtPath\5.15.2" -Recurse | Measure-Object -Property Length -Sum).Sum / 1GB
            $elapsed = (Get-Date) - $startTime

            Write-Host "已安装: $([math]::Round($currentSize, 2))GB | 耗时: $($elapsed.ToString('hh\:mm\:ss'))" -ForegroundColor Green

            # 检查关键文件
            $qtCore = Test-Path "$qtPath\5.15.2\mingw81_64\bin\Qt5Core.dll"
            $qtCreator = Test-Path "$qtPath\Tools\QtCreator\bin\qtcreator.exe"

            if ($qtCore -and $qtCreator) {
                Write-Host "✅ 安装完成！" -ForegroundColor Green
                break
            }
        }

        Start-Sleep -Seconds 30
    }
}

# 在另一个PowerShell窗口中运行监控
# Monitor-QtInstallation
```

#### 📦 离线安装包安装流程

**步骤1：下载离线安装包**
```powershell
# 离线包下载脚本
function Download-QtOfflineInstaller {
    $url = "https://mirrors.tuna.tsinghua.edu.cn/qt/archive/qt/5.15/5.15.2/qt-opensource-windows-x86-5.15.2.exe"
    $output = "qt-opensource-windows-x86-5.15.2.exe"

    Write-Host "=== 下载Qt5.15.2离线安装包 ===" -ForegroundColor Cyan
    Write-Host "下载地址: $url" -ForegroundColor Yellow
    Write-Host "保存位置: $output" -ForegroundColor Yellow

    # 使用Invoke-WebRequest下载
    try {
        $progressPreference = 'Continue'
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing

        # 验证文件大小
        $fileSize = (Get-Item $output).Length / 1GB
        Write-Host "下载完成，文件大小: $([math]::Round($fileSize, 2))GB" -ForegroundColor Green

        # 验证文件完整性
        $hash = Get-FileHash $output -Algorithm SHA256
        Write-Host "SHA256: $($hash.Hash)" -ForegroundColor Yellow

    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Download-QtOfflineInstaller
```

**步骤2：离线安装执行**
```cmd
REM 离线安装命令
REM 静默安装（高级用户）
qt-opensource-windows-x86-5.15.2.exe --script install_script.qs

REM 图形界面安装（推荐）
qt-opensource-windows-x86-5.15.2.exe
```

**步骤3：离线安装脚本**
```javascript
// install_script.qs - Qt静默安装脚本
function Controller() {
    installer.autoRejectMessageBoxes();
    installer.installationFinished.connect(function() {
        gui.clickButton(buttons.NextButton);
    })
}

Controller.prototype.WelcomePageCallback = function() {
    gui.clickButton(buttons.NextButton, 3000);
}

Controller.prototype.CredentialsPageCallback = function() {
    gui.clickButton(buttons.SkipButton);
}

Controller.prototype.IntroductionPageCallback = function() {
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.TargetDirectoryPageCallback = function() {
    gui.currentPageWidget().TargetDirectoryLineEdit.setText("C:\\Qt");
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.ComponentSelectionPageCallback = function() {
    var widget = gui.currentPageWidget();

    // 选择Qt 5.15.2组件
    widget.selectComponent("qt.qt5.5152.win64_mingw81");
    widget.selectComponent("qt.qt5.5152.qtmqtt");

    // 选择工具
    widget.selectComponent("qt.tools.qtcreator");
    widget.selectComponent("qt.tools.mingw810_64");

    gui.clickButton(buttons.NextButton);
}

Controller.prototype.LicenseAgreementPageCallback = function() {
    gui.currentPageWidget().AcceptLicenseRadioButton.setChecked(true);
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.StartMenuDirectoryPageCallback = function() {
    gui.clickButton(buttons.NextButton);
}

Controller.prototype.ReadyForInstallationPageCallback = function() {
    gui.clickButton(buttons.InstallButton);
}

Controller.prototype.FinishedPageCallback = function() {
    var checkBoxForm = gui.currentPageWidget().LaunchQtCreatorCheckBoxForm;
    if (checkBoxForm && checkBoxForm.launchQtCreatorCheckBox) {
        checkBoxForm.launchQtCreatorCheckBox.checked = false;
    }
    gui.clickButton(buttons.FinishButton);
}
```

#### 🎯 组件选择建议

**必需组件清单**：
```
✅ 核心必需组件
├─ Qt 5.15.2 LTS
│  ├─ MinGW 8.1.0 64-bit ✅ 编译器工具链
│  ├─ Qt5Mqtt ✅ MQTT协议支持（ShengFan.exe必需）
│  ├─ Qt Charts ⚠️ 图表功能（可选）
│  └─ Sources ❌ 源代码（不推荐，占用空间大）
├─ Qt Creator 4.15.x ✅ 集成开发环境
├─ MinGW 8.1.0 ✅ 编译器
└─ CMake 3.19+ ⚠️ 构建工具（可选）

❌ 不推荐组件
├─ Qt Design Studio ❌ 设计工具（占用空间大）
├─ Qt Quick 3D ❌ 3D功能（ShengFan.exe不需要）
├─ Qt WebEngine ❌ 浏览器引擎（体积大）
└─ Android/iOS支持 ❌ 移动平台（不需要）
```

**组件大小参考**：
```
📊 磁盘空间占用
├─ Qt 5.15.2 MinGW 64-bit: ~2.1GB
├─ Qt5Mqtt模块: ~15MB
├─ Qt Creator: ~400MB
├─ MinGW 8.1.0: ~1.2GB
├─ Qt Charts: ~50MB
├─ 文档和示例: ~800MB
└─ 总计（推荐配置）: ~4.5GB
```

#### 📁 安装路径规划

**推荐路径结构**：
```
📂 C:\Qt\ (推荐根目录)
├─ 5.15.2\                    # Qt版本目录
│  ├─ mingw81_64\             # MinGW 64位编译器版本
│  │  ├─ bin\                 # 可执行文件和DLL
│  │  ├─ include\             # 头文件
│  │  ├─ lib\                 # 库文件
│  │  └─ plugins\             # 插件
│  └─ Src\                    # 源代码（可选）
├─ Tools\                     # 开发工具
│  ├─ QtCreator\              # Qt Creator IDE
│  ├─ mingw810_64\            # MinGW编译器
│  └─ CMake_64\               # CMake构建工具
└─ Docs\                      # 文档（可选）
```

**路径选择原则**：
```
✅ 推荐做法
├─ 使用默认路径 C:\Qt
├─ 避免包含中文字符
├─ 避免包含空格
├─ 选择SSD磁盘（提升性能）
└─ 确保有足够空间（20GB+）

❌ 避免的路径
├─ C:\Program Files\ (权限问题)
├─ C:\用户\文档\ (中文路径)
├─ D:\My Qt Files\ (包含空格)
├─ 网络驱动器 (性能问题)
└─ 临时目录 (可能被清理)
```

**自定义路径配置**：
```powershell
# 自定义安装路径验证脚本
function Test-QtInstallPath {
    param(
        [string]$Path = "C:\Qt"
    )

    Write-Host "=== Qt安装路径验证 ===" -ForegroundColor Cyan
    Write-Host "目标路径: $Path" -ForegroundColor Yellow

    # 检查路径是否包含中文
    if ($Path -match '[\u4e00-\u9fa5]') {
        Write-Host "❌ 路径包含中文字符，可能导致编译问题" -ForegroundColor Red
        return $false
    }

    # 检查路径是否包含空格
    if ($Path -match '\s') {
        Write-Host "❌ 路径包含空格，可能导致构建问题" -ForegroundColor Red
        return $false
    }

    # 检查磁盘空间
    $drive = Split-Path -Qualifier $Path
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq $drive}).FreeSpace / 1GB

    if ($freeSpace -lt 20) {
        Write-Host "❌ 磁盘空间不足，需要至少20GB，当前可用: $([math]::Round($freeSpace, 2))GB" -ForegroundColor Red
        return $false
    }

    # 检查写入权限
    try {
        $testFile = Join-Path $Path "test_write_permission.tmp"
        New-Item -Path $testFile -ItemType File -Force | Out-Null
        Remove-Item -Path $testFile -Force
        Write-Host "✅ 写入权限正常" -ForegroundColor Green
    } catch {
        Write-Host "❌ 写入权限不足，请以管理员身份运行" -ForegroundColor Red
        return $false
    }

    Write-Host "✅ 路径验证通过" -ForegroundColor Green
    return $true
}

# 使用示例
Test-QtInstallPath -Path "C:\Qt"
```

### 2.3 环境变量配置

#### 🔧 Qt5路径添加到PATH

**自动配置脚本**：
```powershell
# Qt5环境变量自动配置脚本
function Set-QtEnvironment {
    param(
        [string]$QtPath = "C:\Qt\5.15.2\mingw81_64",
        [string]$QtToolsPath = "C:\Qt\Tools\mingw810_64"
    )

    Write-Host "=== Qt5环境变量配置 ===" -ForegroundColor Cyan

    # 验证路径存在
    if (-not (Test-Path $QtPath)) {
        Write-Host "❌ Qt路径不存在: $QtPath" -ForegroundColor Red
        return $false
    }

    if (-not (Test-Path $QtToolsPath)) {
        Write-Host "❌ Qt工具路径不存在: $QtToolsPath" -ForegroundColor Red
        return $false
    }

    # 获取当前PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

    # 要添加的路径
    $pathsToAdd = @(
        "$QtPath\bin",
        "$QtToolsPath\bin"
    )

    $pathsAdded = @()

    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$pathToAdd;$currentPath"
            $pathsAdded += $pathToAdd
            Write-Host "✅ 添加路径: $pathToAdd" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 路径已存在: $pathToAdd" -ForegroundColor Yellow
        }
    }

    if ($pathsAdded.Count -gt 0) {
        # 设置用户环境变量
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, "User")
        Write-Host "✅ PATH环境变量已更新" -ForegroundColor Green
        Write-Host "⚠️ 请重启命令行窗口使环境变量生效" -ForegroundColor Yellow
    }

    return $true
}

Set-QtEnvironment
```

**手动配置方法**：
```cmd
REM 方法1：使用setx命令（永久设置）
setx PATH "%PATH%;C:\Qt\5.15.2\mingw81_64\bin;C:\Qt\Tools\mingw810_64\bin"

REM 方法2：使用set命令（临时设置）
set PATH=%PATH%;C:\Qt\5.15.2\mingw81_64\bin;C:\Qt\Tools\mingw810_64\bin

REM 方法3：图形界面设置
REM 1. Win+R 输入 sysdm.cpl
REM 2. 高级 → 环境变量
REM 3. 用户变量 → PATH → 编辑
REM 4. 新建 → 添加Qt路径
```

#### 🎯 QTDIR环境变量设置

**QTDIR变量配置**：
```powershell
# QTDIR环境变量设置
function Set-QtDirEnvironment {
    param(
        [string]$QtDir = "C:\Qt\5.15.2\mingw81_64"
    )

    Write-Host "=== QTDIR环境变量设置 ===" -ForegroundColor Cyan

    if (-not (Test-Path $QtDir)) {
        Write-Host "❌ Qt目录不存在: $QtDir" -ForegroundColor Red
        return $false
    }

    # 设置QTDIR环境变量
    [Environment]::SetEnvironmentVariable("QTDIR", $QtDir, "User")
    Write-Host "✅ QTDIR设置为: $QtDir" -ForegroundColor Green

    # 设置Qt5_DIR（CMake使用）
    [Environment]::SetEnvironmentVariable("Qt5_DIR", "$QtDir\lib\cmake\Qt5", "User")
    Write-Host "✅ Qt5_DIR设置为: $QtDir\lib\cmake\Qt5" -ForegroundColor Green

    # 验证设置
    $qtdir = [Environment]::GetEnvironmentVariable("QTDIR", "User")
    $qt5dir = [Environment]::GetEnvironmentVariable("Qt5_DIR", "User")

    Write-Host "当前QTDIR: $qtdir" -ForegroundColor Yellow
    Write-Host "当前Qt5_DIR: $qt5dir" -ForegroundColor Yellow

    return $true
}

Set-QtDirEnvironment
```

**批处理配置脚本**：
```batch
@echo off
REM Qt5环境变量批量配置脚本
echo ================================
echo Qt5环境变量配置工具
echo ================================

set QT_ROOT=C:\Qt\5.15.2\mingw81_64
set QT_TOOLS=C:\Qt\Tools\mingw810_64

echo 检查Qt安装路径...
if not exist "%QT_ROOT%\bin\qmake.exe" (
    echo ❌ 错误：未找到qmake.exe，请检查Qt安装路径
    pause
    exit /b 1
)

echo ✅ Qt安装路径验证成功

echo 设置环境变量...
setx QTDIR "%QT_ROOT%"
setx Qt5_DIR "%QT_ROOT%\lib\cmake\Qt5"
setx PATH "%PATH%;%QT_ROOT%\bin;%QT_TOOLS%\bin"

echo ✅ 环境变量设置完成
echo.
echo 📋 设置的环境变量：
echo   QTDIR = %QT_ROOT%
echo   Qt5_DIR = %QT_ROOT%\lib\cmake\Qt5
echo   PATH = 已添加Qt相关路径
echo.
echo ⚠️ 请重启命令行窗口使环境变量生效
pause
```

#### ✅ 验证环境变量配置

**环境变量验证脚本**：
```powershell
# Qt5环境变量验证脚本
function Test-QtEnvironment {
    Write-Host "=== Qt5环境变量验证 ===" -ForegroundColor Cyan

    $allPassed = $true

    # 检查QTDIR
    $qtdir = [Environment]::GetEnvironmentVariable("QTDIR", "User")
    if ($qtdir -and (Test-Path $qtdir)) {
        Write-Host "✅ QTDIR: $qtdir" -ForegroundColor Green
    } else {
        Write-Host "❌ QTDIR未设置或路径无效" -ForegroundColor Red
        $allPassed = $false
    }

    # 检查Qt5_DIR
    $qt5dir = [Environment]::GetEnvironmentVariable("Qt5_DIR", "User")
    if ($qt5dir -and (Test-Path $qt5dir)) {
        Write-Host "✅ Qt5_DIR: $qt5dir" -ForegroundColor Green
    } else {
        Write-Host "❌ Qt5_DIR未设置或路径无效" -ForegroundColor Red
        $allPassed = $false
    }

    # 检查PATH中的Qt路径
    $path = [Environment]::GetEnvironmentVariable("PATH", "User")
    $qtPaths = @("Qt", "mingw")
    $foundPaths = @()

    foreach ($qtPath in $qtPaths) {
        if ($path -like "*$qtPath*") {
            $foundPaths += $qtPath
        }
    }

    if ($foundPaths.Count -gt 0) {
        Write-Host "✅ PATH包含Qt相关路径: $($foundPaths -join ', ')" -ForegroundColor Green
    } else {
        Write-Host "❌ PATH中未找到Qt相关路径" -ForegroundColor Red
        $allPassed = $false
    }

    # 测试命令可用性
    Write-Host "`n命令可用性测试:" -ForegroundColor Yellow

    $commands = @("qmake", "gcc", "g++", "mingw32-make")
    foreach ($cmd in $commands) {
        try {
            $null = Get-Command $cmd -ErrorAction Stop
            Write-Host "✅ $cmd 可用" -ForegroundColor Green
        } catch {
            Write-Host "❌ $cmd 不可用" -ForegroundColor Red
            $allPassed = $false
        }
    }

    if ($allPassed) {
        Write-Host "`n🎉 环境变量配置验证通过！" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️ 环境变量配置存在问题，请检查上述错误" -ForegroundColor Yellow
    }

    return $allPassed
}

Test-QtEnvironment
```

### 2.4 安装验证

#### 🔍 Qt5库文件检查

**核心库文件验证**：
```powershell
# Qt5核心库文件检查脚本
function Test-QtLibraries {
    param(
        [string]$QtPath = "C:\Qt\5.15.2\mingw81_64"
    )

    Write-Host "=== Qt5库文件完整性检查 ===" -ForegroundColor Cyan

    # 核心库文件列表
    $coreLibraries = @(
        "bin\Qt5Core.dll",
        "bin\Qt5Gui.dll",
        "bin\Qt5Widgets.dll",
        "bin\Qt5Network.dll",
        "bin\Qt5Mqtt.dll",
        "bin\Qt5PrintSupport.dll",
        "bin\Qt5Svg.dll"
    )

    # 运行时库文件
    $runtimeLibraries = @(
        "bin\libgcc_s_dw2-1.dll",
        "bin\libstdc++-6.dll",
        "bin\libwinpthread-1.dll"
    )

    # 开发工具
    $developmentTools = @(
        "bin\qmake.exe",
        "bin\moc.exe",
        "bin\uic.exe",
        "bin\rcc.exe"
    )

    $allFiles = $coreLibraries + $runtimeLibraries + $developmentTools
    $missingFiles = @()
    $foundFiles = @()

    Write-Host "检查路径: $QtPath" -ForegroundColor Yellow

    foreach ($file in $allFiles) {
        $fullPath = Join-Path $QtPath $file
        if (Test-Path $fullPath) {
            $version = (Get-ItemProperty $fullPath).VersionInfo.FileVersion
            Write-Host "✅ $file - 版本: $version" -ForegroundColor Green
            $foundFiles += $file
        } else {
            Write-Host "❌ $file - 缺失" -ForegroundColor Red
            $missingFiles += $file
        }
    }

    # 统计结果
    Write-Host "`n📊 检查结果统计:" -ForegroundColor Cyan
    Write-Host "  找到文件: $($foundFiles.Count)/$($allFiles.Count)" -ForegroundColor Green
    Write-Host "  缺失文件: $($missingFiles.Count)" -ForegroundColor Red

    if ($missingFiles.Count -eq 0) {
        Write-Host "🎉 所有必需文件检查通过！" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️ 发现缺失文件，建议重新安装Qt5" -ForegroundColor Yellow
        return $false
    }
}

Test-QtLibraries
```

#### 📋 版本信息验证

**版本一致性检查**：
```powershell
# Qt5版本信息验证脚本
function Test-QtVersion {
    Write-Host "=== Qt5版本信息验证 ===" -ForegroundColor Cyan

    # 检查qmake版本
    try {
        $qmakeVersion = qmake -version 2>&1
        Write-Host "qmake版本信息:" -ForegroundColor Yellow
        Write-Host $qmakeVersion -ForegroundColor White

        # 提取Qt版本号
        if ($qmakeVersion -match "Qt version (\d+\.\d+\.\d+)") {
            $qtVersion = $matches[1]
            if ($qtVersion -eq "5.15.2") {
                Write-Host "✅ Qt版本正确: $qtVersion" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Qt版本不匹配，期望5.15.2，实际: $qtVersion" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "❌ 无法获取qmake版本信息" -ForegroundColor Red
        return $false
    }

    # 检查编译器版本
    try {
        $gccVersion = gcc --version 2>&1 | Select-Object -First 1
        Write-Host "`n编译器版本:" -ForegroundColor Yellow
        Write-Host $gccVersion -ForegroundColor White

        if ($gccVersion -match "8\.1\.0") {
            Write-Host "✅ MinGW版本正确: 8.1.0" -ForegroundColor Green
        } else {
            Write-Host "⚠️ MinGW版本可能不匹配" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 无法获取编译器版本信息" -ForegroundColor Red
        return $false
    }

    # 检查Qt Creator版本
    $qtCreatorPath = "C:\Qt\Tools\QtCreator\bin\qtcreator.exe"
    if (Test-Path $qtCreatorPath) {
        $qtCreatorVersion = (Get-ItemProperty $qtCreatorPath).VersionInfo.FileVersion
        Write-Host "`nQt Creator版本: $qtCreatorVersion" -ForegroundColor Yellow
        Write-Host "✅ Qt Creator已安装" -ForegroundColor Green
    } else {
        Write-Host "❌ Qt Creator未找到" -ForegroundColor Red
    }

    return $true
}

Test-QtVersion
```

#### 🧪 基本功能测试

**Hello World测试项目**：
```cpp
// hello_qt.cpp - Qt5基本功能测试
#include <QApplication>
#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QMessageBox>
#include <QtMqtt/QMqttClient>

class TestWidget : public QWidget {
    Q_OBJECT

public:
    TestWidget(QWidget *parent = nullptr) : QWidget(parent) {
        setupUI();
        testMqtt();
    }

private slots:
    void showTestResult() {
        QMessageBox::information(this, "Qt5测试",
            "Qt5安装验证成功！\n"
            "✅ Qt5Core - 正常\n"
            "✅ Qt5Gui - 正常\n"
            "✅ Qt5Widgets - 正常\n"
            "✅ Qt5Mqtt - 正常");
    }

private:
    void setupUI() {
        setWindowTitle("Qt5安装验证测试");
        setFixedSize(300, 200);

        auto *layout = new QVBoxLayout(this);
        auto *label = new QLabel("Qt5安装验证测试", this);
        label->setAlignment(Qt::AlignCenter);

        auto *button = new QPushButton("测试Qt5功能", this);
        connect(button, &QPushButton::clicked, this, &TestWidget::showTestResult);

        layout->addWidget(label);
        layout->addWidget(button);
    }

    void testMqtt() {
        // 测试MQTT模块是否可用
        QMqttClient *client = new QMqttClient(this);
        client->setHostname("test.mosquitto.org");
        client->setPort(1883);
        // 不实际连接，只测试模块加载
    }
};

#include "hello_qt.moc"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    TestWidget widget;
    widget.show();

    return app.exec();
}
```

**测试项目构建脚本**：
```batch
@echo off
REM Qt5功能测试项目构建脚本
echo ================================
echo Qt5功能测试项目构建
echo ================================

REM 创建测试目录
mkdir qt5_test 2>nul
cd qt5_test

REM 创建测试源文件
echo 创建测试源文件...
(
echo #include ^<QApplication^>
echo #include ^<QWidget^>
echo #include ^<QLabel^>
echo #include ^<QVBoxLayout^>
echo #include ^<QPushButton^>
echo #include ^<QMessageBox^>
echo.
echo int main^(int argc, char *argv[]^) {
echo     QApplication app^(argc, argv^);
echo     QWidget window;
echo     window.setWindowTitle^("Qt5安装验证"^);
echo     window.setFixedSize^(300, 150^);
echo.
echo     QVBoxLayout *layout = new QVBoxLayout^(&window^);
echo     QLabel *label = new QLabel^("Qt5安装验证成功！"^);
echo     label-^>setAlignment^(Qt::AlignCenter^);
echo     layout-^>addWidget^(label^);
echo.
echo     window.show^(^);
echo     return app.exec^(^);
echo }
) > main.cpp

REM 创建项目文件
echo 创建项目文件...
(
echo QT += core gui widgets
echo CONFIG += c++11
echo TARGET = qt5_test
echo SOURCES += main.cpp
) > qt5_test.pro

REM 生成Makefile
echo 生成Makefile...
qmake qt5_test.pro

REM 编译项目
echo 编译项目...
mingw32-make

REM 检查编译结果
if exist "release\qt5_test.exe" (
    echo ✅ 编译成功！
    echo 🚀 启动测试程序...
    start release\qt5_test.exe
) else (
    echo ❌ 编译失败，请检查Qt5安装
)

pause
```

---

## 📝 第二部分总结

### ✅ 完成的安装配置

通过第二部分的学习，您应该已经完成了以下安装配置：

1. **Qt5获取方式** 🟢
   - ✅ 了解官方在线安装器使用方法
   - ✅ 掌握离线安装包下载和使用
   - ✅ 配置国内镜像站点加速下载
   - ✅ 理解各种方式的优缺点对比

2. **详细安装步骤** 🟢
   - ✅ 完成在线安装器完整流程
   - ✅ 掌握离线安装包安装流程
   - ✅ 学会组件选择和安装路径规划
   - ✅ 了解静默安装脚本使用

3. **环境变量配置** 🟢
   - ✅ 正确添加Qt5路径到PATH
   - ✅ 设置QTDIR和Qt5_DIR环境变量
   - ✅ 验证环境变量配置正确性

4. **安装验证** 🟢
   - ✅ 检查Qt5库文件完整性
   - ✅ 验证版本信息一致性
   - ✅ 完成基本功能测试

### 🎯 下一步行动

现在您已经完成了Qt5的安装配置，可以继续进行：
- **第三部分：开发环境** 🟡 - Qt Creator IDE和编译器详细设置
- **第四部分：集成应用** 🔴 - 高级集成和实际案例演示
- **第五部分：附录资源** 🔴 - 故障排除和参考资料

---

# 第三部分：开发环境配置 🟡

## 3. Qt Creator IDE与编译器配置 🟡

### 3.1 Qt Creator IDE配置

#### 🚀 Qt Creator下载和安装

**Qt Creator获取方式**：
```
📦 Qt Creator安装选项

选项1：随Qt5一起安装（推荐）
✅ 版本匹配度高
✅ 配置自动完成
✅ 无需额外下载
❌ 无法单独更新

选项2：独立安装最新版
✅ 功能更新及时
✅ 可独立更新
❌ 需要手动配置
❌ 可能存在兼容性问题

选项3：便携版安装
✅ 无需安装权限
✅ 可多版本共存
❌ 配置相对复杂
❌ 更新需要手动处理
```

**Qt Creator启动和初始配置**：
```powershell
# Qt Creator启动检查脚本
function Start-QtCreator {
    $qtCreatorPath = "C:\Qt\Tools\QtCreator\bin\qtcreator.exe"

    Write-Host "=== Qt Creator启动检查 ===" -ForegroundColor Cyan

    if (-not (Test-Path $qtCreatorPath)) {
        Write-Host "❌ Qt Creator未找到: $qtCreatorPath" -ForegroundColor Red
        Write-Host "请检查Qt5安装是否包含Qt Creator组件" -ForegroundColor Yellow
        return $false
    }

    # 检查Qt Creator版本
    $version = (Get-ItemProperty $qtCreatorPath).VersionInfo.FileVersion
    Write-Host "✅ Qt Creator版本: $version" -ForegroundColor Green

    # 检查配置目录
    $configDir = "$env:APPDATA\QtProject\qtcreator"
    if (-not (Test-Path $configDir)) {
        Write-Host "📁 创建配置目录: $configDir" -ForegroundColor Yellow
        New-Item -Path $configDir -ItemType Directory -Force | Out-Null
    }

    Write-Host "🚀 启动Qt Creator..." -ForegroundColor Green
    Start-Process $qtCreatorPath

    return $true
}

Start-QtCreator
```

**首次启动配置向导**：
```
📋 Qt Creator首次启动配置

🔹 欢迎界面
   - 选择"Configure Qt Creator"
   - 跳过账户登录（可选）

🔹 Kit检测
   - 自动检测已安装的Qt版本
   - 验证编译器配置
   - 确认调试器设置

🔹 主题和界面
   - 选择界面主题（推荐Dark主题）
   - 设置字体大小（推荐12pt）
   - 配置代码编辑器样式

🔹 插件管理
   - 启用必需插件
   - 禁用不需要的插件
   - 配置代码补全设置
```

#### ⚙️ 工具链配置（MinGW、MSVC）

**MinGW工具链配置**：
```
🔧 MinGW 8.1.0配置步骤

1️⃣ 打开工具链设置
   Tools → Options → Kits → Compilers

2️⃣ 添加MinGW编译器
   - 点击"Add" → "GCC"
   - Name: MinGW 8.1.0 64bit
   - Compiler path: C:\Qt\Tools\mingw810_64\bin\gcc.exe

3️⃣ 配置C++编译器
   - 点击"Add" → "GCC"
   - Name: MinGW 8.1.0 64bit C++
   - Compiler path: C:\Qt\Tools\mingw810_64\bin\g++.exe

4️⃣ 验证编译器
   - 检查"ABI"显示为"x86-windows-msys-pe-64bit"
   - 确认编译器版本为8.1.0
```

**MSVC工具链配置（可选）**：
```
🔧 MSVC 2019配置步骤

前置条件：
✅ 安装Visual Studio 2019或Build Tools
✅ 包含MSVC v142编译器工具集
✅ Windows 10 SDK

1️⃣ 自动检测MSVC
   Tools → Options → Kits → Compilers
   - Qt Creator通常自动检测MSVC

2️⃣ 手动添加MSVC（如需要）
   - 点击"Add" → "Microsoft Visual C++ Compiler"
   - 选择对应的MSVC版本

3️⃣ 配置编译器路径
   - C编译器: cl.exe路径
   - C++编译器: cl.exe路径
   - 确认ABI为"x86-windows-msvc2019-pe-64bit"
```

**编译器配置验证脚本**：
```powershell
# 编译器配置验证脚本
function Test-CompilerConfiguration {
    Write-Host "=== 编译器配置验证 ===" -ForegroundColor Cyan

    # 检查MinGW编译器
    $mingwGcc = "C:\Qt\Tools\mingw810_64\bin\gcc.exe"
    $mingwGpp = "C:\Qt\Tools\mingw810_64\bin\g++.exe"

    if (Test-Path $mingwGcc) {
        $gccVersion = & $mingwGcc --version 2>&1 | Select-Object -First 1
        Write-Host "✅ MinGW GCC: $gccVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ MinGW GCC未找到" -ForegroundColor Red
    }

    if (Test-Path $mingwGpp) {
        $gppVersion = & $mingwGpp --version 2>&1 | Select-Object -First 1
        Write-Host "✅ MinGW G++: $gppVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ MinGW G++未找到" -ForegroundColor Red
    }

    # 检查make工具
    $mingwMake = "C:\Qt\Tools\mingw810_64\bin\mingw32-make.exe"
    if (Test-Path $mingwMake) {
        Write-Host "✅ MinGW Make工具可用" -ForegroundColor Green
    } else {
        Write-Host "❌ MinGW Make工具未找到" -ForegroundColor Red
    }

    # 检查MSVC（可选）
    try {
        $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
        if (Test-Path $vsWhere) {
            $vsInstalls = & $vsWhere -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath
            if ($vsInstalls) {
                Write-Host "✅ MSVC编译器可用: $($vsInstalls.Count)个安装" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "⚠️ MSVC编译器检查跳过（可选）" -ForegroundColor Yellow
    }
}

Test-CompilerConfiguration
```

#### 🎛️ Kit配置和管理

**Qt Kit配置详解**：
```
🎯 Kit配置要素

Kit = Qt版本 + 编译器 + 调试器 + CMake + 设备

核心组件：
├─ Qt Version: Qt 5.15.2 (mingw81_64)
├─ C Compiler: MinGW 8.1.0 64bit
├─ C++ Compiler: MinGW 8.1.0 64bit C++
├─ Debugger: MinGW GDB
├─ CMake Tool: CMake 3.19+
└─ Device Type: Desktop
```

**创建自定义Kit**：
```
📝 自定义Kit创建步骤

1️⃣ 打开Kit管理
   Tools → Options → Kits → Kits

2️⃣ 添加新Kit
   - 点击"Add"
   - Name: "Qt 5.15.2 MinGW 64bit (ShengFan)"

3️⃣ 配置Kit组件
   - Device type: Desktop
   - Sysroot: (留空)
   - Qt version: Qt 5.15.2 (mingw81_64)
   - C compiler: MinGW 8.1.0 64bit
   - C++ compiler: MinGW 8.1.0 64bit C++
   - Debugger: System GDB at C:\Qt\Tools\mingw810_64\bin\gdb.exe
   - CMake Tool: System CMake

4️⃣ 高级设置
   - CMake generator: MinGW Makefiles
   - CMake configuration: (默认)
   - Environment: (默认)

5️⃣ 验证Kit
   - 确保所有组件显示为绿色图标
   - 检查错误和警告信息
```

**Kit配置验证脚本**：
```powershell
# Qt Kit配置验证脚本
function Test-QtKitConfiguration {
    Write-Host "=== Qt Kit配置验证 ===" -ForegroundColor Cyan

    # 检查Qt版本配置
    $qtVersions = @()
    $qtInstallPath = "C:\Qt"

    if (Test-Path $qtInstallPath) {
        $qtDirs = Get-ChildItem -Path $qtInstallPath -Directory | Where-Object {$_.Name -match "^\d+\.\d+\.\d+$"}
        foreach ($qtDir in $qtDirs) {
            $qmakePath = Join-Path $qtDir.FullName "mingw81_64\bin\qmake.exe"
            if (Test-Path $qmakePath) {
                $qtVersions += $qtDir.Name
                Write-Host "✅ 发现Qt版本: $($qtDir.Name)" -ForegroundColor Green
            }
        }
    }

    if ($qtVersions.Count -eq 0) {
        Write-Host "❌ 未找到有效的Qt版本" -ForegroundColor Red
        return $false
    }

    # 检查编译器工具
    $requiredTools = @{
        "qmake" = "C:\Qt\5.15.2\mingw81_64\bin\qmake.exe"
        "gcc" = "C:\Qt\Tools\mingw810_64\bin\gcc.exe"
        "g++" = "C:\Qt\Tools\mingw810_64\bin\g++.exe"
        "gdb" = "C:\Qt\Tools\mingw810_64\bin\gdb.exe"
        "make" = "C:\Qt\Tools\mingw810_64\bin\mingw32-make.exe"
    }

    foreach ($tool in $requiredTools.GetEnumerator()) {
        if (Test-Path $tool.Value) {
            Write-Host "✅ $($tool.Key): 可用" -ForegroundColor Green
        } else {
            Write-Host "❌ $($tool.Key): 缺失" -ForegroundColor Red
        }
    }

    Write-Host "📋 Kit配置建议:" -ForegroundColor Yellow
    Write-Host "  - 使用Qt 5.15.2版本" -ForegroundColor White
    Write-Host "  - 选择MinGW 8.1.0编译器" -ForegroundColor White
    Write-Host "  - 启用GDB调试器" -ForegroundColor White
    Write-Host "  - 配置CMake支持" -ForegroundColor White

    return $true
}

Test-QtKitConfiguration
```

#### 🔌 插件和扩展推荐

**必需插件清单**：
```
✅ 核心必需插件（默认启用）
├─ Qt Quick Designer - Qt Quick界面设计
├─ Qt Designer - Widget界面设计
├─ C++ Support - C++语言支持
├─ Qt Support - Qt框架支持
├─ Debugger Support - 调试器支持
├─ CMake Support - CMake构建支持
└─ Git Support - Git版本控制

⚠️ 推荐启用插件
├─ Code Pasting - 代码分享功能
├─ Image Viewer - 图片查看器
├─ Task List - 任务列表管理
├─ Todo - TODO注释管理
└─ Text Editor - 增强文本编辑

❌ 可禁用插件（节省资源）
├─ Android Support - Android开发
├─ iOS Support - iOS开发
├─ QNX Support - QNX系统支持
├─ Remote Linux - 远程Linux开发
└─ Welcome Screen - 欢迎界面（可选）
```

**插件配置优化**：
```
🔧 插件配置建议

1️⃣ 打开插件管理
   Help → About Plugins

2️⃣ 禁用不需要的插件
   - 取消勾选移动平台相关插件
   - 禁用不使用的语言支持
   - 关闭不需要的设计工具

3️⃣ 配置代码补全
   Tools → Options → Text Editor → Completion
   - 启用"Autocomplete common keywords"
   - 设置"Automatically insert matching characters"
   - 配置"Case sensitivity"为"First Letter"

4️⃣ 配置代码格式化
   Tools → Options → C++ → Code Style
   - 选择"Qt"代码风格
   - 自定义缩进和括号样式
   - 配置命名约定

5️⃣ 配置构建设置
   Tools → Options → Build & Run → General
   - 设置默认构建目录
   - 配置并行编译数量
   - 启用"Stop on first build error"
```

### 3.2 编译器设置

#### 🔨 MinGW编译器配置

**MinGW编译器详细配置**：
```
🎯 MinGW 8.1.0配置要点

编译器特性：
├─ 版本: GCC 8.1.0
├─ 架构: x86_64-w64-mingw32
├─ 线程模型: posix
├─ 异常处理: seh
└─ C++标准: C++11/14/17支持

配置路径：
├─ 编译器根目录: C:\Qt\Tools\mingw810_64
├─ GCC路径: C:\Qt\Tools\mingw810_64\bin\gcc.exe
├─ G++路径: C:\Qt\Tools\mingw810_64\bin\g++.exe
├─ Make工具: C:\Qt\Tools\mingw810_64\bin\mingw32-make.exe
└─ 调试器: C:\Qt\Tools\mingw810_64\bin\gdb.exe
```

**编译器性能优化配置**：
```
⚡ 编译性能优化设置

1️⃣ 并行编译配置
   Tools → Options → Build & Run → General
   - "Concurrent build jobs": 设置为CPU核心数
   - 推荐值: 4-8（根据CPU核心数）

2️⃣ 编译器标志优化
   项目设置 → Build Settings → Build Steps
   - Debug模式: -g -O0 (调试信息，无优化)
   - Release模式: -O2 -DNDEBUG (优化，无调试)

3️⃣ 链接器优化
   - 启用增量链接: -Wl,--incremental
   - 减少符号表: -Wl,--strip-debug
   - 优化大小: -Wl,--gc-sections

4️⃣ 预编译头文件
   - 启用PCH支持
   - 配置常用头文件预编译
   - 减少编译时间
```

**MinGW编译器测试脚本**：
```cpp
// mingw_test.cpp - MinGW编译器功能测试
#include <iostream>
#include <vector>
#include <memory>
#include <thread>
#include <chrono>

// 测试C++11特性
void test_cpp11_features() {
    std::cout << "=== C++11特性测试 ===" << std::endl;

    // auto关键字
    auto numbers = std::vector<int>{1, 2, 3, 4, 5};

    // 范围for循环
    for (const auto& num : numbers) {
        std::cout << "数字: " << num << std::endl;
    }

    // 智能指针
    auto ptr = std::make_unique<int>(42);
    std::cout << "智能指针值: " << *ptr << std::endl;

    // Lambda表达式
    auto lambda = [](int x) { return x * 2; };
    std::cout << "Lambda结果: " << lambda(21) << std::endl;
}

// 测试多线程支持
void test_threading() {
    std::cout << "\n=== 多线程测试 ===" << std::endl;

    auto worker = []() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        std::cout << "线程ID: " << std::this_thread::get_id() << std::endl;
    };

    std::thread t1(worker);
    std::thread t2(worker);

    t1.join();
    t2.join();

    std::cout << "多线程测试完成" << std::endl;
}

int main() {
    std::cout << "MinGW编译器功能测试" << std::endl;
    std::cout << "编译器版本: " << __VERSION__ << std::endl;
    std::cout << "C++标准: " << __cplusplus << std::endl;

    test_cpp11_features();
    test_threading();

    std::cout << "\n✅ 所有测试通过！" << std::endl;
    return 0;
}
```

**编译测试脚本**：
```batch
@echo off
REM MinGW编译器测试脚本
echo ================================
echo MinGW编译器功能测试
echo ================================

REM 创建测试目录
mkdir mingw_test 2>nul
cd mingw_test

REM 创建测试源文件
echo 创建测试源文件...
(
echo #include ^<iostream^>
echo #include ^<vector^>
echo #include ^<memory^>
echo.
echo int main^(^) {
echo     std::cout ^<^< "MinGW编译器测试" ^<^< std::endl;
echo     auto vec = std::vector^<int^>{1, 2, 3};
echo     for ^(const auto^& item : vec^) {
echo         std::cout ^<^< "项目: " ^<^< item ^<^< std::endl;
echo     }
echo     return 0;
echo }
) > test.cpp

REM 编译测试
echo 编译测试程序...
g++ -std=c++11 -o test.exe test.cpp

REM 检查编译结果
if exist "test.exe" (
    echo ✅ 编译成功！
    echo 🚀 运行测试程序...
    test.exe
) else (
    echo ❌ 编译失败
    echo 请检查MinGW编译器配置
)

echo.
echo 📋 编译器信息:
gcc --version
echo.
echo 📋 支持的C++标准:
g++ -dM -E -x c++ /dev/null | findstr __cplusplus

pause
```

#### 🏢 MSVC编译器配置（可选）

**MSVC编译器配置要点**：
```
🎯 MSVC 2019配置说明

使用场景：
✅ 需要最佳Windows平台优化
✅ 使用Microsoft特定API
✅ 商业项目部署要求
✅ 与Visual Studio项目集成

配置要求：
├─ Visual Studio 2019或更新版本
├─ MSVC v142编译器工具集
├─ Windows 10 SDK
├─ CMake 3.16+支持
└─ Qt 5.15.2 MSVC版本

注意事项：
⚠️ 需要Visual Studio许可证
⚠️ 编译速度相对较慢
⚠️ 调试信息文件较大
⚠️ 部署需要VC++ Redistributable
```

**MSVC与MinGW对比**：
```
📊 编译器对比分析

| 特性 | MinGW 8.1.0 | MSVC 2019 | 推荐度 |
|------|-------------|-----------|--------|
| **许可证** | 开源免费 | 商业许可 | MinGW ⭐⭐⭐⭐⭐ |
| **编译速度** | 快速 | 中等 | MinGW ⭐⭐⭐⭐ |
| **优化程度** | 良好 | 优秀 | MSVC ⭐⭐⭐⭐ |
| **调试支持** | GDB | Visual Studio | MSVC ⭐⭐⭐⭐⭐ |
| **部署复杂度** | 简单 | 复杂 | MinGW ⭐⭐⭐⭐⭐ |
| **标准支持** | C++17 | C++20 | MSVC ⭐⭐⭐⭐ |
| **ShengFan.exe兼容** | 完美 | 良好 | MinGW ⭐⭐⭐⭐⭐ |

🎯 推荐选择：
- 个人学习/开发: MinGW 8.1.0
- 商业项目: 根据需求选择
- ShengFan.exe项目: MinGW 8.1.0
```

#### 🎯 编译器版本选择建议

**版本选择决策树**：
```
🌳 编译器版本选择指南

项目类型判断：
├─ ShengFan.exe相关项目
│  └─ 选择: MinGW 8.1.0 ✅
│     理由: 完全兼容，部署简单
│
├─ 新Qt5项目开发
│  ├─ 个人/学习项目
│  │  └─ 选择: MinGW 8.1.0 ✅
│  │     理由: 免费，易用，性能好
│  │
│  └─ 商业项目
│     ├─ 需要最佳性能 → MSVC 2019
│     ├─ 需要快速开发 → MinGW 8.1.0
│     └─ 需要跨平台 → MinGW 8.1.0
│
└─ 维护现有项目
   ├─ 原项目使用MinGW → 继续使用MinGW
   ├─ 原项目使用MSVC → 继续使用MSVC
   └─ 混合项目 → 统一为MinGW
```

#### 🐛 调试器配置

**GDB调试器配置**：
```
🔍 GDB调试器设置

基本配置：
├─ 调试器路径: C:\Qt\Tools\mingw810_64\bin\gdb.exe
├─ 调试器类型: GDB
├─ 版本要求: GDB 8.1+
├─ 架构支持: x86_64
└─ 符号格式: DWARF

高级配置：
├─ 启用Pretty Printing（美化显示）
├─ 配置断点行为
├─ 设置调试输出格式
├─ 配置远程调试（可选）
└─ 优化调试性能
```

**调试器配置脚本**：
```powershell
# GDB调试器配置验证脚本
function Test-GdbConfiguration {
    Write-Host "=== GDB调试器配置验证 ===" -ForegroundColor Cyan

    $gdbPath = "C:\Qt\Tools\mingw810_64\bin\gdb.exe"

    if (-not (Test-Path $gdbPath)) {
        Write-Host "❌ GDB调试器未找到: $gdbPath" -ForegroundColor Red
        return $false
    }

    # 检查GDB版本
    try {
        $gdbVersion = & $gdbPath --version 2>&1 | Select-Object -First 1
        Write-Host "✅ GDB版本: $gdbVersion" -ForegroundColor Green

        # 检查Python支持（Pretty Printing需要）
        $pythonSupport = & $gdbPath --batch --ex "python print('Python支持正常')" --ex quit 2>&1
        if ($pythonSupport -like "*Python支持正常*") {
            Write-Host "✅ GDB Python支持: 可用" -ForegroundColor Green
        } else {
            Write-Host "⚠️ GDB Python支持: 不可用（Pretty Printing受限）" -ForegroundColor Yellow
        }

    } catch {
        Write-Host "❌ GDB版本检查失败" -ForegroundColor Red
        return $false
    }

    # 检查调试符号支持
    Write-Host "📋 调试器配置建议:" -ForegroundColor Yellow
    Write-Host "  - 编译时添加 -g 标志生成调试信息" -ForegroundColor White
    Write-Host "  - Release版本使用 -O2 -g 保留部分调试信息" -ForegroundColor White
    Write-Host "  - 启用Qt Creator的Pretty Printing功能" -ForegroundColor White

    return $true
}

Test-GdbConfiguration
```

### 3.3 项目创建和编译测试

#### 🆕 创建Hello World项目

**Qt Widgets项目创建步骤**：
```
📝 Hello World项目创建

1️⃣ 新建项目
   File → New File or Project
   - 选择"Application"
   - 选择"Qt Widgets Application"
   - 点击"Choose"

2️⃣ 项目配置
   - Project name: HelloQt5
   - Location: C:\Qt\Projects\HelloQt5
   - Build system: qmake (推荐)

3️⃣ Kit选择
   - 选择"Qt 5.15.2 MinGW 64bit"
   - 确保Kit状态为绿色

4️⃣ 类信息配置
   - Class name: MainWindow
   - Source filename: mainwindow.cpp
   - Header filename: mainwindow.h
   - Form filename: mainwindow.ui

5️⃣ 版本控制
   - 选择"Git"（推荐）
   - 或选择"None"
```

**Hello World源代码示例**：
```cpp
// main.cpp - 应用程序入口
#include "mainwindow.h"
#include <QApplication>
#include <QStyleFactory>
#include <QDir>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("Hello Qt5");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Qt5学习项目");

    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 创建主窗口
    MainWindow window;
    window.show();

    return app.exec();
}
```

```cpp
// mainwindow.h - 主窗口头文件
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QTimer>
#include <QDateTime>

QT_BEGIN_NAMESPACE
class QWidget;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onButtonClicked();           // 按钮点击槽函数
    void updateTime();                // 更新时间槽函数

private:
    void setupUI();                   // 设置用户界面
    void setupConnections();          // 设置信号连接

    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_buttonLayout;

    QLabel *m_titleLabel;
    QLabel *m_timeLabel;
    QLabel *m_statusLabel;

    QPushButton *m_helloButton;
    QPushButton *m_aboutButton;
    QPushButton *m_exitButton;

    QTimer *m_timer;                  // 定时器
    int m_clickCount;                 // 点击计数
};

#endif // MAINWINDOW_H
```

```cpp
// mainwindow.cpp - 主窗口实现
#include "mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_clickCount(0)
{
    setupUI();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("Hello Qt5 - 学习示例");
    setMinimumSize(400, 300);
    resize(500, 350);

    // 启动定时器
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &MainWindow::updateTime);
    m_timer->start(1000); // 每秒更新一次

    updateTime(); // 立即更新一次时间
}

MainWindow::~MainWindow()
{
    // Qt的父子关系会自动清理内存，无需手动delete
}

void MainWindow::setupUI()
{
    // 创建中央窗口部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 创建布局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_buttonLayout = new QHBoxLayout();

    // 创建标签
    m_titleLabel = new QLabel("🎉 欢迎使用Qt5！", this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;");

    m_timeLabel = new QLabel(this);
    m_timeLabel->setAlignment(Qt::AlignCenter);
    m_timeLabel->setStyleSheet("font-size: 14px; color: #666; margin: 5px;");

    m_statusLabel = new QLabel("状态：就绪", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("font-size: 12px; color: #888; margin: 5px;");

    // 创建按钮
    m_helloButton = new QPushButton("👋 说Hello", this);
    m_aboutButton = new QPushButton("ℹ️ 关于", this);
    m_exitButton = new QPushButton("❌ 退出", this);

    // 设置按钮样式
    QString buttonStyle =
        "QPushButton {"
        "    background-color: #4CAF50;"
        "    border: none;"
        "    color: white;"
        "    padding: 8px 16px;"
        "    font-size: 14px;"
        "    border-radius: 4px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #45a049;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3d8b40;"
        "}";

    m_helloButton->setStyleSheet(buttonStyle);
    m_aboutButton->setStyleSheet(buttonStyle.replace("#4CAF50", "#2196F3").replace("#45a049", "#1976D2").replace("#3d8b40", "#1565C0"));
    m_exitButton->setStyleSheet(buttonStyle.replace("#4CAF50", "#f44336").replace("#45a049", "#d32f2f").replace("#3d8b40", "#c62828"));

    // 布局组装
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_timeLabel);
    m_mainLayout->addStretch(); // 添加弹性空间

    m_buttonLayout->addWidget(m_helloButton);
    m_buttonLayout->addWidget(m_aboutButton);
    m_buttonLayout->addWidget(m_exitButton);

    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_statusLabel);
}

void MainWindow::setupConnections()
{
    // 连接信号和槽
    connect(m_helloButton, &QPushButton::clicked, this, &MainWindow::onButtonClicked);
    connect(m_aboutButton, &QPushButton::clicked, [this]() {
        QMessageBox::about(this, "关于Hello Qt5",
            "<h3>Hello Qt5 学习示例</h3>"
            "<p>这是一个Qt5学习项目示例</p>"
            "<p><b>Qt版本:</b> " + QString(QT_VERSION_STR) + "</p>"
            "<p><b>编译器:</b> " + QString(__VERSION__) + "</p>"
            "<p><b>构建时间:</b> " + QString(__DATE__) + " " + QString(__TIME__) + "</p>");
    });
    connect(m_exitButton, &QPushButton::clicked, this, &QWidget::close);
}

void MainWindow::onButtonClicked()
{
    m_clickCount++;

    QString message = QString("Hello Qt5! 这是第 %1 次点击").arg(m_clickCount);
    QMessageBox::information(this, "Hello", message);

    m_statusLabel->setText(QString("状态：已点击 %1 次").arg(m_clickCount));
}

void MainWindow::updateTime()
{
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss dddd");
    m_timeLabel->setText("当前时间：" + currentTime);
}
```

**项目文件配置**：
```pro
# HelloQt5.pro - qmake项目文件
QT += core gui widgets

CONFIG += c++11

TARGET = HelloQt5
TEMPLATE = app

# 定义应用程序版本
VERSION = 1.0.0
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

# 源文件
SOURCES += \
    main.cpp \
    mainwindow.cpp

# 头文件
HEADERS += \
    mainwindow.h

# UI文件（如果使用Designer）
# FORMS += \
#     mainwindow.ui

# 资源文件（可选）
# RESOURCES += \
#     resources.qrc

# Windows特定配置
win32 {
    # 设置应用程序图标
    RC_ICONS = app.ico

    # 设置版本信息
    VERSION_PE_HEADER = $$VERSION
}

# 调试配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_BUILD
    TARGET = $$TARGET"_debug"
}

# 发布配置
CONFIG(release, debug|release) {
    DEFINES += RELEASE_BUILD
    # 优化设置
    QMAKE_CXXFLAGS_RELEASE += -O2
}

# 输出目录配置
DESTDIR = $$PWD/bin
OBJECTS_DIR = $$PWD/build/obj
MOC_DIR = $$PWD/build/moc
RCC_DIR = $$PWD/build/rcc
UI_DIR = $$PWD/build/ui
```

#### ⚡ 编译和运行测试

**编译流程详解**：
```
🔨 Qt项目编译流程

1️⃣ qmake阶段
   - 解析.pro项目文件
   - 生成Makefile
   - 配置编译参数

2️⃣ moc阶段（Meta-Object Compiler）
   - 处理Q_OBJECT宏
   - 生成信号槽机制代码
   - 创建moc_*.cpp文件

3️⃣ uic阶段（User Interface Compiler）
   - 编译.ui界面文件
   - 生成ui_*.h头文件

4️⃣ rcc阶段（Resource Compiler）
   - 编译.qrc资源文件
   - 生成qrc_*.cpp文件

5️⃣ 编译阶段
   - 编译所有.cpp源文件
   - 链接Qt库和系统库
   - 生成可执行文件
```

**编译测试脚本**：
```batch
@echo off
REM Qt项目编译测试脚本
echo ================================
echo Qt项目编译测试
echo ================================

set PROJECT_NAME=HelloQt5
set BUILD_DIR=build

REM 检查项目文件
if not exist "%PROJECT_NAME%.pro" (
    echo ❌ 错误：未找到项目文件 %PROJECT_NAME%.pro
    pause
    exit /b 1
)

echo ✅ 找到项目文件: %PROJECT_NAME%.pro

REM 创建构建目录
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
cd "%BUILD_DIR%"

echo 📋 开始编译流程...

REM 1. qmake阶段
echo [1/4] 运行qmake...
qmake ..\%PROJECT_NAME%.pro
if %ERRORLEVEL% neq 0 (
    echo ❌ qmake失败
    pause
    exit /b 1
)
echo ✅ qmake完成

REM 2. 编译阶段
echo [2/4] 编译项目...
mingw32-make
if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

REM 3. 检查输出文件
echo [3/4] 检查输出文件...
if exist "release\%PROJECT_NAME%.exe" (
    echo ✅ 发现Release版本: release\%PROJECT_NAME%.exe
    set EXE_PATH=release\%PROJECT_NAME%.exe
) else if exist "debug\%PROJECT_NAME%.exe" (
    echo ✅ 发现Debug版本: debug\%PROJECT_NAME%.exe
    set EXE_PATH=debug\%PROJECT_NAME%.exe
) else (
    echo ❌ 未找到可执行文件
    pause
    exit /b 1
)

REM 4. 运行测试
echo [4/4] 运行测试...
echo 🚀 启动应用程序...
start "" "%EXE_PATH%"

echo.
echo 🎉 编译测试完成！
echo 📁 可执行文件位置: %CD%\%EXE_PATH%
echo.
pause
```

**编译性能监控脚本**：
```powershell
# Qt编译性能监控脚本
function Measure-QtBuildPerformance {
    param(
        [string]$ProjectPath = ".",
        [string]$BuildType = "release"
    )

    Write-Host "=== Qt编译性能监控 ===" -ForegroundColor Cyan

    $startTime = Get-Date
    $projectFile = Get-ChildItem -Path $ProjectPath -Filter "*.pro" | Select-Object -First 1

    if (-not $projectFile) {
        Write-Host "❌ 未找到.pro项目文件" -ForegroundColor Red
        return
    }

    Write-Host "📁 项目文件: $($projectFile.Name)" -ForegroundColor Yellow
    Write-Host "🔨 构建类型: $BuildType" -ForegroundColor Yellow
    Write-Host "⏰ 开始时间: $startTime" -ForegroundColor Yellow

    # 创建构建目录
    $buildDir = "build_$BuildType"
    if (-not (Test-Path $buildDir)) {
        New-Item -Path $buildDir -ItemType Directory | Out-Null
    }

    Set-Location $buildDir

    try {
        # qmake阶段
        Write-Host "`n[1/3] 运行qmake..." -ForegroundColor Green
        $qmakeStart = Get-Date
        $qmakeResult = qmake "..\$($projectFile.Name)" 2>&1
        $qmakeEnd = Get-Date
        $qmakeTime = ($qmakeEnd - $qmakeStart).TotalSeconds

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ qmake完成 (耗时: $([math]::Round($qmakeTime, 2))秒)" -ForegroundColor Green
        } else {
            Write-Host "❌ qmake失败" -ForegroundColor Red
            Write-Host $qmakeResult -ForegroundColor Red
            return
        }

        # 编译阶段
        Write-Host "`n[2/3] 编译项目..." -ForegroundColor Green
        $compileStart = Get-Date
        $compileResult = mingw32-make 2>&1
        $compileEnd = Get-Date
        $compileTime = ($compileEnd - $compileStart).TotalSeconds

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 编译完成 (耗时: $([math]::Round($compileTime, 2))秒)" -ForegroundColor Green
        } else {
            Write-Host "❌ 编译失败" -ForegroundColor Red
            Write-Host $compileResult -ForegroundColor Red
            return
        }

        # 检查输出
        Write-Host "`n[3/3] 检查输出..." -ForegroundColor Green
        $exeFiles = Get-ChildItem -Path . -Filter "*.exe" -Recurse

        if ($exeFiles) {
            foreach ($exe in $exeFiles) {
                $size = [math]::Round($exe.Length / 1MB, 2)
                Write-Host "✅ 生成文件: $($exe.FullName) (大小: ${size}MB)" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ 未找到可执行文件" -ForegroundColor Red
        }

    } finally {
        Set-Location ..
    }

    $endTime = Get-Date
    $totalTime = ($endTime - $startTime).TotalSeconds

    Write-Host "`n📊 编译性能统计:" -ForegroundColor Cyan
    Write-Host "  qmake耗时: $([math]::Round($qmakeTime, 2))秒" -ForegroundColor White
    Write-Host "  编译耗时: $([math]::Round($compileTime, 2))秒" -ForegroundColor White
    Write-Host "  总计耗时: $([math]::Round($totalTime, 2))秒" -ForegroundColor White
    Write-Host "  结束时间: $endTime" -ForegroundColor White
}

# 使用示例
# Measure-QtBuildPerformance -ProjectPath "." -BuildType "release"
```

#### 🔧 常见编译问题解决

**编译错误诊断表**：
```
🚨 常见编译错误及解决方案

错误1：'qmake' 不是内部或外部命令
原因：Qt路径未添加到PATH环境变量
解决：添加 C:\Qt\5.15.2\mingw81_64\bin 到PATH

错误2：No rule to make target 'release'
原因：Makefile生成失败或项目文件错误
解决：删除Makefile，重新运行qmake

错误3：undefined reference to `vtable for ClassName'
原因：Q_OBJECT宏的类未运行moc
解决：清理项目，重新构建

错误4：fatal error: QtWidgets/QApplication: No such file
原因：Qt模块未正确包含
解决：在.pro文件中添加 QT += widgets

错误5：LNK1104: cannot open file 'Qt5Core.lib'
原因：链接器找不到Qt库文件
解决：检查Qt安装路径和Kit配置

错误6：error: 'class QWidget' has no member named 'connect'
原因：信号槽语法错误或头文件缺失
解决：检查#include和connect语法

错误7：mingw32-make: *** No targets specified and no makefile found
原因：未在正确目录运行make或Makefile缺失
解决：确保在包含Makefile的目录运行

错误8：collect2.exe: error: ld returned 1 exit status
原因：链接阶段失败，通常是库文件问题
解决：检查依赖库和链接器设置
```

**编译问题自动诊断脚本**：
```powershell
# Qt编译问题自动诊断脚本
function Diagnose-QtBuildIssues {
    Write-Host "=== Qt编译问题自动诊断 ===" -ForegroundColor Cyan

    $issues = @()

    # 检查环境变量
    Write-Host "`n🔍 检查环境变量..." -ForegroundColor Yellow

    $path = $env:PATH
    $qtPaths = @(
        "C:\Qt\5.15.2\mingw81_64\bin",
        "C:\Qt\Tools\mingw810_64\bin"
    )

    foreach ($qtPath in $qtPaths) {
        if ($path -like "*$qtPath*") {
            Write-Host "✅ PATH包含: $qtPath" -ForegroundColor Green
        } else {
            Write-Host "❌ PATH缺失: $qtPath" -ForegroundColor Red
            $issues += "PATH环境变量缺失Qt路径: $qtPath"
        }
    }

    # 检查关键工具
    Write-Host "`n🔍 检查编译工具..." -ForegroundColor Yellow

    $tools = @{
        "qmake" = "qmake.exe"
        "gcc" = "gcc.exe"
        "g++" = "g++.exe"
        "mingw32-make" = "mingw32-make.exe"
    }

    foreach ($tool in $tools.GetEnumerator()) {
        try {
            $null = Get-Command $tool.Value -ErrorAction Stop
            Write-Host "✅ $($tool.Key): 可用" -ForegroundColor Green
        } catch {
            Write-Host "❌ $($tool.Key): 不可用" -ForegroundColor Red
            $issues += "$($tool.Key) 工具不可用，请检查Qt安装"
        }
    }

    # 检查Qt库文件
    Write-Host "`n🔍 检查Qt库文件..." -ForegroundColor Yellow

    $qtLibPath = "C:\Qt\5.15.2\mingw81_64\lib"
    $coreLibs = @("Qt5Core.dll", "Qt5Gui.dll", "Qt5Widgets.dll")

    foreach ($lib in $coreLibs) {
        $libPath = Join-Path "C:\Qt\5.15.2\mingw81_64\bin" $lib
        if (Test-Path $libPath) {
            Write-Host "✅ $lib: 存在" -ForegroundColor Green
        } else {
            Write-Host "❌ $lib: 缺失" -ForegroundColor Red
            $issues += "Qt核心库缺失: $lib"
        }
    }

    # 检查项目文件
    Write-Host "`n🔍 检查项目文件..." -ForegroundColor Yellow

    $proFiles = Get-ChildItem -Filter "*.pro"
    if ($proFiles) {
        foreach ($proFile in $proFiles) {
            Write-Host "✅ 项目文件: $($proFile.Name)" -ForegroundColor Green

            # 检查项目文件内容
            $content = Get-Content $proFile.FullName
            if ($content -like "*QT += *") {
                Write-Host "✅ Qt模块配置: 正常" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Qt模块配置: 可能缺失" -ForegroundColor Yellow
                $issues += "项目文件可能缺少Qt模块配置"
            }
        }
    } else {
        Write-Host "❌ 未找到.pro项目文件" -ForegroundColor Red
        $issues += "当前目录缺少.pro项目文件"
    }

    # 输出诊断结果
    Write-Host "`n📋 诊断结果:" -ForegroundColor Cyan

    if ($issues.Count -eq 0) {
        Write-Host "🎉 未发现明显问题，编译环境配置正常！" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 发现 $($issues.Count) 个潜在问题:" -ForegroundColor Yellow
        for ($i = 0; $i -lt $issues.Count; $i++) {
            Write-Host "  $($i + 1). $($issues[$i])" -ForegroundColor Red
        }

        Write-Host "`n💡 建议解决方案:" -ForegroundColor Cyan
        Write-Host "  1. 重新安装Qt5.15.2并确保选择MinGW组件" -ForegroundColor White
        Write-Host "  2. 配置环境变量PATH包含Qt相关路径" -ForegroundColor White
        Write-Host "  3. 重启命令行窗口使环境变量生效" -ForegroundColor White
        Write-Host "  4. 使用Qt Creator创建新项目进行测试" -ForegroundColor White
    }
}

Diagnose-QtBuildIssues
```

### 3.4 与ShengFan.exe项目集成

#### 📂 现有项目导入方法

**ShengFan.exe项目分析**：
```powershell
# ShengFan.exe项目结构分析脚本
function Analyze-ShengFanProject {
    param(
        [string]$ProjectPath = "."
    )

    Write-Host "=== ShengFan.exe项目结构分析 ===" -ForegroundColor Cyan

    # 检查主要文件
    $mainFiles = @{
        "ShengFan.exe" = "主程序文件"
        "*.dll" = "依赖库文件"
        "*.pro" = "Qt项目文件"
        "*.cpp" = "C++源文件"
        "*.h" = "头文件"
        "*.ui" = "界面文件"
    }

    Write-Host "📁 项目路径: $ProjectPath" -ForegroundColor Yellow

    foreach ($pattern in $mainFiles.GetEnumerator()) {
        $files = Get-ChildItem -Path $ProjectPath -Filter $pattern.Key -Recurse
        if ($files) {
            Write-Host "✅ $($pattern.Value): 找到 $($files.Count) 个文件" -ForegroundColor Green
            if ($pattern.Key -eq "*.dll") {
                # 分析DLL依赖
                $qtDlls = $files | Where-Object {$_.Name -like "Qt5*"}
                $runtimeDlls = $files | Where-Object {$_.Name -like "lib*"}
                Write-Host "  - Qt5库: $($qtDlls.Count) 个" -ForegroundColor White
                Write-Host "  - 运行时库: $($runtimeDlls.Count) 个" -ForegroundColor White
            }
        } else {
            Write-Host "⚠️ $($pattern.Value): 未找到" -ForegroundColor Yellow
        }
    }

    # 检查Qt版本兼容性
    $qt5Core = Get-ChildItem -Path $ProjectPath -Filter "Qt5Core.dll" -Recurse | Select-Object -First 1
    if ($qt5Core) {
        $version = (Get-ItemProperty $qt5Core.FullName).VersionInfo.FileVersion
        Write-Host "`n🔍 检测到Qt版本: $version" -ForegroundColor Yellow

        if ($version -like "5.15.2*") {
            Write-Host "✅ 版本兼容: 与Qt 5.15.2完全兼容" -ForegroundColor Green
        } elseif ($version -like "5.15.*") {
            Write-Host "⚠️ 版本兼容: 与Qt 5.15.x基本兼容" -ForegroundColor Yellow
        } else {
            Write-Host "❌ 版本兼容: 可能存在兼容性问题" -ForegroundColor Red
        }
    }
}

Analyze-ShengFanProject
```

**项目导入步骤**：
```
📝 ShengFan.exe项目导入流程

1️⃣ 准备工作
   - 备份原项目文件
   - 确保Qt Creator已正确配置
   - 验证Qt 5.15.2环境

2️⃣ 导入项目
   File → Open File or Project
   - 选择ShengFan.pro文件
   - 或选择包含源文件的目录

3️⃣ Kit配置
   - 选择"Qt 5.15.2 MinGW 64bit"
   - 确保编译器版本匹配
   - 验证调试器配置

4️⃣ 项目设置检查
   - 检查.pro文件配置
   - 验证源文件路径
   - 确认依赖库设置

5️⃣ 编译测试
   - 清理项目 (Build → Clean All)
   - 重新构建 (Build → Rebuild All)
   - 检查编译输出
```

#### 🔗 依赖库配置

**Qt5依赖库配置**：
```pro
# ShengFan.pro - 依赖库配置示例
QT += core gui widgets network mqtt printsupport svg

CONFIG += c++11

TARGET = ShengFan
TEMPLATE = app

# 版本信息
VERSION = 1.0.0
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

# 源文件配置
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    # 其他源文件...

HEADERS += \
    mainwindow.h \
    # 其他头文件...

# 资源文件
RESOURCES += \
    resources.qrc

# Windows特定配置
win32 {
    # 应用程序图标
    RC_ICONS = app.ico

    # 版本信息
    VERSION_PE_HEADER = $$VERSION

    # 依赖库路径（如果需要）
    LIBS += -L$$PWD/libs/

    # 静态链接运行时库（可选）
    # CONFIG += static

    # 部署配置
    CONFIG(release, debug|release) {
        # Release版本优化
        QMAKE_CXXFLAGS_RELEASE += -O2

        # 去除调试信息
        QMAKE_LFLAGS_RELEASE += -Wl,--strip-debug
    }
}

# 包含路径
INCLUDEPATH += \
    $$PWD/include \
    $$PWD/third_party

# 库文件链接
LIBS += \
    -lws2_32 \
    -lwinmm \
    # 其他系统库...

# 预编译头文件（可选）
PRECOMPILED_HEADER = stable.h

# 输出目录配置
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/bin/debug
    TARGET = $$TARGET"_debug"
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/bin/release
}

# 中间文件目录
OBJECTS_DIR = $$PWD/build/obj
MOC_DIR = $$PWD/build/moc
RCC_DIR = $$PWD/build/rcc
UI_DIR = $$PWD/build/ui
```

**依赖库检查脚本**：
```powershell
# ShengFan.exe依赖库检查脚本
function Check-ShengFanDependencies {
    param(
        [string]$ExePath = ".\ShengFan.exe"
    )

    Write-Host "=== ShengFan.exe依赖库检查 ===" -ForegroundColor Cyan

    if (-not (Test-Path $ExePath)) {
        Write-Host "❌ 未找到ShengFan.exe: $ExePath" -ForegroundColor Red
        return $false
    }

    Write-Host "📁 检查文件: $ExePath" -ForegroundColor Yellow

    # 使用Dependencies工具分析（如果可用）
    $depsPath = "C:\Tools\Dependencies\Dependencies.exe"
    if (Test-Path $depsPath) {
        Write-Host "🔍 使用Dependencies工具分析..." -ForegroundColor Green
        Start-Process $depsPath -ArgumentList "`"$ExePath`"" -Wait
    }

    # 手动检查关键DLL
    $requiredDlls = @(
        "Qt5Core.dll",
        "Qt5Gui.dll",
        "Qt5Widgets.dll",
        "Qt5Network.dll",
        "Qt5Mqtt.dll",
        "libgcc_s_dw2-1.dll",
        "libstdc++-6.dll",
        "libwinpthread-1.dll"
    )

    $exeDir = Split-Path $ExePath -Parent
    $missingDlls = @()

    Write-Host "`n📋 检查必需DLL文件:" -ForegroundColor Yellow

    foreach ($dll in $requiredDlls) {
        $dllPath = Join-Path $exeDir $dll
        if (Test-Path $dllPath) {
            $version = (Get-ItemProperty $dllPath).VersionInfo.FileVersion
            Write-Host "✅ $dll - 版本: $version" -ForegroundColor Green
        } else {
            Write-Host "❌ $dll - 缺失" -ForegroundColor Red
            $missingDlls += $dll
        }
    }

    if ($missingDlls.Count -gt 0) {
        Write-Host "`n⚠️ 缺失的DLL文件:" -ForegroundColor Yellow
        foreach ($dll in $missingDlls) {
            Write-Host "  - $dll" -ForegroundColor Red
        }

        Write-Host "`n💡 解决方案:" -ForegroundColor Cyan
        Write-Host "  1. 从Qt安装目录复制缺失的DLL文件" -ForegroundColor White
        Write-Host "  2. 使用windeployqt工具自动部署" -ForegroundColor White
        Write-Host "  3. 配置PATH环境变量包含Qt库路径" -ForegroundColor White
    } else {
        Write-Host "`n🎉 所有必需DLL文件检查通过！" -ForegroundColor Green
    }

    return ($missingDlls.Count -eq 0)
}

Check-ShengFanDependencies
```

#### ⚙️ 编译设置调整

**编译器标志优化**：
```
🎯 ShengFan.exe编译优化配置

Debug版本配置：
├─ 编译标志: -g -O0 -DDEBUG
├─ 链接标志: -g
├─ 调试信息: 完整保留
├─ 优化级别: 无优化
└─ 符号表: 完整保留

Release版本配置：
├─ 编译标志: -O2 -DNDEBUG -DQT_NO_DEBUG_OUTPUT
├─ 链接标志: -Wl,--strip-debug -s
├─ 调试信息: 移除
├─ 优化级别: -O2 (速度优化)
└─ 符号表: 精简

特殊配置：
├─ MQTT支持: -DQT_MQTT_LIB
├─ 网络功能: -DQT_NETWORK_LIB
├─ 打印支持: -DQT_PRINTSUPPORT_LIB
├─ SVG支持: -DQT_SVG_LIB
└─ 中文支持: -DUNICODE -D_UNICODE
```

**自定义编译配置**：
```pro
# 高级编译配置
CONFIG += c++11 warn_on

# 编译器警告设置
QMAKE_CXXFLAGS += -Wall -Wextra -Wpedantic

# Debug配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_BUILD QT_QML_DEBUG
    QMAKE_CXXFLAGS += -g -O0
    TARGET = $$TARGET"_debug"

    # 内存检查（可选）
    # QMAKE_CXXFLAGS += -fsanitize=address
    # QMAKE_LFLAGS += -fsanitize=address
}

# Release配置
CONFIG(release, debug|release) {
    DEFINES += RELEASE_BUILD QT_NO_DEBUG_OUTPUT QT_NO_WARNING_OUTPUT

    # 优化设置
    QMAKE_CXXFLAGS_RELEASE += -O2 -march=native

    # 链接器优化
    QMAKE_LFLAGS_RELEASE += -Wl,--gc-sections -Wl,--strip-debug

    # 减小文件大小
    QMAKE_LFLAGS_RELEASE += -s
}

# 平台特定优化
win32 {
    # Windows优化
    QMAKE_CXXFLAGS += -mthreads
    QMAKE_LFLAGS += -mthreads

    # 静态链接C++运行时（可选）
    # QMAKE_LFLAGS += -static-libgcc -static-libstdc++
}

# 并行编译
QMAKE_CXXFLAGS += -j$$system(nproc)
```

#### 📦 运行时库部署

**windeployqt自动部署**：
```batch
@echo off
REM ShengFan.exe自动部署脚本
echo ================================
echo ShengFan.exe运行时库部署
echo ================================

set EXE_NAME=ShengFan.exe
set DEPLOY_DIR=deploy
set QT_DIR=C:\Qt\5.15.2\mingw81_64

REM 检查可执行文件
if not exist "%EXE_NAME%" (
    echo ❌ 错误：未找到 %EXE_NAME%
    echo 请确保已编译生成可执行文件
    pause
    exit /b 1
)

echo ✅ 找到可执行文件: %EXE_NAME%

REM 创建部署目录
if exist "%DEPLOY_DIR%" (
    echo 🗑️ 清理旧的部署目录...
    rmdir /s /q "%DEPLOY_DIR%"
)
mkdir "%DEPLOY_DIR%"

REM 复制主程序
echo 📁 复制主程序文件...
copy "%EXE_NAME%" "%DEPLOY_DIR%\"

REM 使用windeployqt自动部署
echo 🚀 运行windeployqt自动部署...
"%QT_DIR%\bin\windeployqt.exe" --debug-and-release --compiler-runtime "%DEPLOY_DIR%\%EXE_NAME%"

if %ERRORLEVEL% neq 0 (
    echo ❌ windeployqt部署失败
    pause
    exit /b 1
)

echo ✅ windeployqt部署完成

REM 检查部署结果
echo 📋 检查部署结果...
cd "%DEPLOY_DIR%"

echo.
echo 📁 部署文件列表:
dir /b *.dll
echo.

REM 测试运行
echo 🧪 测试部署版本...
echo 启动 %EXE_NAME% 进行测试...
start "" "%EXE_NAME%"

echo.
echo 🎉 部署完成！
echo 📁 部署目录: %CD%
echo 💡 可以将整个 %DEPLOY_DIR% 目录复制到目标机器运行
echo.
pause
```

**手动部署清单**：
```
📦 ShengFan.exe手动部署清单

核心程序文件：
├─ ShengFan.exe                    # 主程序

Qt5核心库：
├─ Qt5Core.dll                     # Qt核心库
├─ Qt5Gui.dll                      # GUI支持
├─ Qt5Widgets.dll                  # 窗口控件
├─ Qt5Network.dll                  # 网络功能
├─ Qt5Mqtt.dll                     # MQTT协议
├─ Qt5PrintSupport.dll             # 打印支持
└─ Qt5Svg.dll                      # SVG支持

MinGW运行时库：
├─ libgcc_s_dw2-1.dll              # GCC运行时
├─ libstdc++-6.dll                 # C++标准库
└─ libwinpthread-1.dll             # 线程支持

Qt5平台插件：
├─ platforms\qwindows.dll          # Windows平台
└─ platforms\qminimal.dll          # 最小平台

Qt5图像格式插件：
├─ imageformats\qico.dll           # ICO格式
├─ imageformats\qjpeg.dll          # JPEG格式
└─ imageformats\qpng.dll           # PNG格式

可选组件：
├─ styles\qwindowsvistastyle.dll   # Windows样式
├─ iconengines\qsvgicon.dll        # SVG图标
└─ bearer\qgenericbearer.dll       # 网络承载
```

---

## 📝 第三部分总结

### ✅ 完成的开发环境配置

通过第三部分的学习，您应该已经完成了以下开发环境配置：

1. **Qt Creator IDE配置** 🟢
   - ✅ 完成Qt Creator下载和安装
   - ✅ 配置MinGW和MSVC工具链
   - ✅ 设置Kit配置和管理
   - ✅ 优化插件和扩展配置

2. **编译器设置** 🟢
   - ✅ 配置MinGW 8.1.0编译器
   - ✅ 了解MSVC编译器选项
   - ✅ 掌握编译器版本选择建议
   - ✅ 完成调试器配置

3. **项目创建和编译测试** 🟢
   - ✅ 创建Hello World测试项目
   - ✅ 完成编译和运行测试
   - ✅ 学会常见编译问题解决
   - ✅ 掌握项目模板使用

4. **与ShengFan.exe项目集成** 🟢
   - ✅ 掌握现有项目导入方法
   - ✅ 配置依赖库和编译设置
   - ✅ 学会运行时库部署方法
   - ✅ 完成项目集成验证

### 🎯 下一步行动

现在您已经完成了开发环境配置，可以继续进行：
- **第四部分：集成应用** 🔴 - Dependencies工具配合使用和OneNET云平台对接
- **第五部分：附录资源** 🔴 - 故障排除指南和参考资料

---

*文档创建时间: 2025年6月29日*
*最后更新: 2025年6月29日*
*文档版本: v1.0.0*
